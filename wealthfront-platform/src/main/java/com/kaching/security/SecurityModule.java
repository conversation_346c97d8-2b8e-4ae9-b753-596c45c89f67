package com.kaching.security;

import static com.google.common.base.Preconditions.checkArgument;
import static com.google.common.base.Preconditions.checkNotNull;
import static com.google.common.collect.Lists.newArrayList;
import static com.google.common.collect.Maps.newHashMap;
import static com.google.inject.name.Names.named;
import static com.kaching.security.Ciphers.aes;
import static com.kaching.security.Ciphers.blowfish;
import static com.kaching.security.Ciphers.cipherGroup;
import static com.kaching.util.Base64.decode;

import java.lang.annotation.Annotation;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.Security;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

import org.bouncycastle.jce.provider.BouncyCastleProvider;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.AbstractModule;
import com.google.inject.Binding;
import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.Key;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.guice.ThreadLocalScope;

public class SecurityModule extends AbstractModule {

  private static final Log log = Log.getLog(SecurityModule.class);
  private static final String SIGNING_ALGORITHM = "HmacSHA1";
  private static final ThreadLocalScope messageDigestThreadLocalScope = new ThreadLocalScope();

  private final List<AnnotationSecret> macs = newArrayList();
  private final List<CipherInfo> cipherInfos = newArrayList();
  private final Map<String, Provider<Cipher>> ciphers = newHashMap();

  private boolean configured = false;

  public SecurityModule() {
  }

  public SecurityModule(List<CipherInfo> cipherInfos) {
    this.cipherInfos.addAll(cipherInfos);
  }

  @Deprecated
  public SecurityModule(Map<String, Provider<Cipher>> ciphers) {
    this.ciphers.putAll(ciphers);
  }

  @Override
  protected void configure() {
    configured = true;

    bind(MessageDigest.class)
        .annotatedWith(MD5.class)
        .toProvider(Md5MessageDigestProvider.class)
        .in(messageDigestThreadLocalScope);

    bind(MessageDigest.class)
        .annotatedWith(SHA1.class)
        .toProvider(Sha1MessageDigestProvider.class)
        .in(messageDigestThreadLocalScope);

    for (final AnnotationSecret p : macs) {
      bind(Mac.class).annotatedWith(p.getAnnotation()).toProvider(() -> createMac(p.getSecret()));
    }

    Security.addProvider(new BouncyCastleProvider());

    for (CipherInfo cipherInfo : cipherInfos) {
      log.info("binding Cipher %s", cipherInfo.getName());
      bind(Cipher.class)
          .annotatedWith(named(cipherInfo.getName()))
          .toProvider(new CipherProvider(cipherInfo))
          .in(Singleton.class);
    }

    for (Entry<String, Provider<Cipher>> entry : ciphers.entrySet()) {
      log.info("binding Cipher %s", entry.getKey());
      bind(Cipher.class)
          .annotatedWith(named(entry.getKey()))
          .toProvider(entry.getValue())
          .in(Singleton.class);
    }
  }

  public static class CipherProvider implements Provider<Cipher> {

    private final CipherInfo cipherInfo;

    public CipherProvider(CipherInfo cipherInfo) {
      this.cipherInfo = cipherInfo;
    }

    @Inject Injector injector;

    @Override
    public Cipher get() {
      String propertiesKey = cipherInfo.getPropertiesKey();
      Option<Cipher> primaryCipher = getPrimaryCipher(cipherInfo.getCipher(), propertiesKey);
      Option<Cipher> secondaryCipher = getSecondaryCipher(cipherInfo.getCipher(), propertiesKey);

      if (primaryCipher.isDefined() && secondaryCipher.isDefined()) {
        return cipherGroup(primaryCipher.getOrThrow(), List.of(secondaryCipher.getOrThrow()));
      } else if (primaryCipher.isDefined()) {
        return primaryCipher.getOrThrow();
      } else {
        return getDefaultCipher(cipherInfo.getCipher(), propertiesKey);
      }
    }

    @VisibleForTesting
    Cipher getDefaultCipher(CipherInfo.Cipher defaultCipherType, String propertiesKey) {
      return createCipher(defaultCipherType, getOptionalProperty(propertiesKey)
          .getOrThrow(new IllegalArgumentException("Cannot find a configuration for " + propertiesKey)));
    }

    @VisibleForTesting
    Option<Cipher> getPrimaryCipher(CipherInfo.Cipher defaultCipherType, String propertiesKey) {
      return getOptionalCipher(defaultCipherType, propertiesKey + ".primary");
    }

    @VisibleForTesting
    Option<Cipher> getSecondaryCipher(CipherInfo.Cipher defaultCipherType, String propertiesKey) {
      return getOptionalCipher(defaultCipherType, propertiesKey + ".secondary");
    }

    @VisibleForTesting
    CipherInfo.Cipher getCipherType(CipherInfo.Cipher defaultCipherType, String propertiesKey) {
      return getOptionalProperty(propertiesKey + ".cipher_type")
          .transform(String::toUpperCase)
          .transform(CipherInfo.Cipher::valueOf)
          .getOrElse(defaultCipherType);
    }

    @VisibleForTesting
    Option<Cipher> getOptionalCipher(CipherInfo.Cipher defaultCipherType, String propertiesKey) {
      return getOptionalProperty(propertiesKey + ".key")
          .transform(key -> createCipher(getCipherType(defaultCipherType, propertiesKey), key));
    }

    @VisibleForTesting
    Cipher createCipher(CipherInfo.Cipher cipherType, String key) {
      checkNotNull(key, "cipher key cannot be null");
      byte[] decodedKey = decode(key);
      checkArgument(decodedKey != null, "key is not base64 encoded");
      return switch (cipherType) {
        case AES -> aes(decode(key), new SecureRandom());
        case BLOWFISH -> blowfish(decode(key));
      };
    }

    @VisibleForTesting
    Option<String> getOptionalProperty(String propertiesKey) {
      Binding<String> binding = injector.getExistingBinding(Key.get(String.class, named(propertiesKey)));
      if (binding != null) {
        return Option.of(binding.getProvider().get());
      }
      return Option.none();
    }

  }

  public static Mac createMac(String secret) {
    try {
      Mac mac = Mac.getInstance(SIGNING_ALGORITHM);
      mac.init(new SecretKeySpec(secret.getBytes(), SIGNING_ALGORITHM));
      return mac;
    } catch (Exception e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * Add a {@link Mac} to the bound algorithms on this module.
   */
  public SecurityModule addHmacSHA1(Annotation annotation, String secret) {
    if (configured) {
      throw new IllegalStateException(
          "Cannot add a security operation after configuration.");
    }
    macs.add(new AnnotationSecret(annotation, secret));
    return this;
  }

  public static class Md5MessageDigestProvider implements Provider<MessageDigest> {

    @Override
    public MessageDigest get() {
      try {
        return MessageDigest.getInstance("MD5");
      } catch (NoSuchAlgorithmException e) {
        throw new IllegalStateException(e);
      }
    }

  }

  public static class Sha1MessageDigestProvider implements Provider<MessageDigest> {

    @Override
    public MessageDigest get() {
      try {
        return MessageDigest.getInstance("SHA1");
      } catch (NoSuchAlgorithmException e) {
        throw new IllegalStateException(e);
      }
    }

  }

  public static class CipherInfo {

    public enum Cipher {
      AES,
      BLOWFISH,
    }

    private final String name;
    private final String propertiesKey;
    private final Cipher cipher;

    public CipherInfo(String name, String propertiesKey, Cipher cipher) {
      this.name = name;
      this.propertiesKey = propertiesKey;
      this.cipher = cipher;
    }

    public String getName() {
      return name;
    }

    public String getPropertiesKey() {
      return propertiesKey;
    }

    public Cipher getCipher() {
      return cipher;
    }

  }

  private class AnnotationSecret {

    private final Annotation annotation;
    private final String secret;

    private AnnotationSecret(Annotation annotation, String secret) {
      this.annotation = annotation;
      this.secret = secret;
    }

    private Annotation getAnnotation() {
      return annotation;
    }

    private String getSecret() {
      return secret;
    }

  }

}
