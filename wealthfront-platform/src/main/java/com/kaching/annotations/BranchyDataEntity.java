package com.kaching.annotations;

import java.util.Set;

import com.kaching.api.EntityDataComponent;
import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@ExposeType(value = ExposeTo.BACKEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
public interface BranchyDataEntity extends EntityDataComponent {

  String getInternalId();

  void setInternalId(String id);

  void setFieldsToNullSet(Set<String> fieldsToNullSet);

  Set<String> getFieldsToNullSet();

}
