package com.kaching.platform.queryengine.progress;

import java.io.PrintStream;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.IntStream;

import org.joda.time.Duration;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.kaching.platform.functional.Unchecked;
import com.kaching.platform.queryengine.progress.ProgressMonitor.StageProgress;
import com.kaching.util.Sleeper;
import com.kaching.util.UncheckedInterruptedException;
import com.kaching.util.functional.Pointer;
import com.kaching.util.functional.Tuple6;
import com.kaching.util.time.NanoTimeProvider;
import com.wealthfront.util.format.TextTable;

@Singleton
public class UnixConsoleProgressDisplayer implements ProgressDisplayer {

  private static final String CLEAR_LINE_RESET_CURSOR = "\033[2K\033[1A\033[2K";
  private static final int PROGRESS_BAR_WIDTH = 40;
  private static final Duration SLEEP_DURATION = Duration.millis(500);
  private static final long NS_PER_MS = 1000000;

  private final PrintStream out;
  private final Pointer<Thread> displayThread = Pointer.pointer();

  public UnixConsoleProgressDisplayer(PrintStream out) {
    this.out = out;
  }

  @Inject ProgressMonitor progressMonitor;
  @Inject Sleeper sleeper;
  @Inject NanoTimeProvider nanoTimeProvider;

  @Override
  public Context displayProgress() {
    displayThread.update(thread -> {
      if (thread != null) {
        return thread;
      }
      thread = new Thread(() -> {
        int lastDisplayedLines = 0;
        boolean done = false;

        while (!done) {
          lastDisplayedLines = renderProgressBars(lastDisplayedLines);
          try {
            sleeper.sleep(SLEEP_DURATION);
          } catch (UncheckedInterruptedException ignored) {
            done = true;
          }
        }

        IntStream.range(0, lastDisplayedLines).forEach($ -> out.print(CLEAR_LINE_RESET_CURSOR));
        out.flush();
      });
      thread.start();
      return thread;
    });

    return () -> displayThread.update(thread -> {
      if (thread == null) {
        return null;
      }
      thread.interrupt();
      Unchecked.run(thread::join);
      return null;
    });
  }

  @VisibleForTesting
  int renderProgressBars(int lastDisplayedLines) {
    IntStream.range(0, lastDisplayedLines).forEach($ -> out.print(CLEAR_LINE_RESET_CURSOR));

    TextTable<Tuple6<?, ?, ?, ?, ?, ?>> table =
        new TextTable<Tuple6<?, ?, ?, ?, ?, ?>>(Tuple6.of("NAME", "", "COMPLETED", "TOTAL", "DURATION", "ETA"))
            .withColumnSeparator(" ")
            .includeHeaderSeparator(false);

    List<StageProgress> progress = new ArrayList<>(progressMonitor.getStages());
    progress.sort(Comparator.comparing(c -> {
      if (c.getWorkCompletedSoFar() == c.getTotalWorkToDo()) {
        return -2L;
      }
      return c.getEstimatedRemainingNanos().getOrElse(-1L);
    }));
    for (StageProgress stage : progress) {
      double ratio = Double.min(((double) stage.getWorkCompletedSoFar()) / stage.getTotalWorkToDo(), 1.);
      int complete = (int) (ratio * PROGRESS_BAR_WIDTH);
      long end = stage.getEndTimeNanos() == -1 ? nanoTimeProvider.get() : stage.getEndTimeNanos();
      Duration runningFor = Duration.millis((end - stage.getStartTimeNanos()) / NS_PER_MS);

      table.withRow(Tuple6.of(
          stage.getStageName(),
          "[" + (stage.getTotalWorkToDo() == Long.MAX_VALUE ?
              Strings.repeat("?", PROGRESS_BAR_WIDTH) :
              Strings.repeat("#", complete) + Strings.repeat(" ", PROGRESS_BAR_WIDTH - complete))
              + "]",
          stage.getWorkCompletedSoFar(),
          stage.getTotalWorkToDo() == Long.MAX_VALUE ? "???" : Long.toString(stage.getTotalWorkToDo()),
          format(runningFor),
          stage.getWorkCompletedSoFar() < stage.getTotalWorkToDo() ?
              stage.getEstimatedRemainingNanos()
                  .transform(ns -> ns / 1000000)
                  .transform(Duration::millis)
                  .transform(this::format)
                  .getOrElse("???") :
              ""
      ));
    }
    String rendered = table.display();
    out.print(rendered);
    out.flush();
    return rendered.split("\n").length;
  }

  private String format(Duration duration) {
    return formatSegment(duration.getStandardHours()) + ":"
        + formatSegment(duration.getStandardMinutes()) + ":"
        + formatSegment(duration.getStandardSeconds());
  }

  private String formatSegment(long number) {
    return Strings.padStart(Long.toString(number), 2, '0');
  }

}
