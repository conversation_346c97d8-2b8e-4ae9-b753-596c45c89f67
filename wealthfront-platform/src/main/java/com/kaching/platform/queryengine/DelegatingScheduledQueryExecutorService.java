package com.kaching.platform.queryengine;

import static com.google.common.collect.Maps.newHashMap;
import static com.kaching.platform.common.logging.Log.getLog;
import static com.kaching.platform.queryengine.QueryEngineModule.API_REQUEST_METADATA_KEY;
import static com.kaching.platform.queryengine.QueryEngineModule.CALL_DEPTH_COUNT_KEY;
import static com.kaching.platform.queryengine.QueryEngineModule.ORIGIN_INFO_KEY;
import static com.kaching.platform.queryengine.QueryEngineModule.QUERY_CLASS_KEY;
import static com.kaching.platform.queryengine.QueryEngineModule.REQUEST_AUTHORIZATION_TOKEN_KEY;
import static com.kaching.platform.queryengine.QueryEngineModule.TRACER_KEY;
import static com.kaching.platform.queryengine.QueryEngineModule.TRACE_KEY;
import static com.kaching.platform.queryengine.QueryExecutorServices.getOrFail;
import static com.kaching.platform.queryengine.Tracers.localRequest;
import static java.util.Collections.synchronizedList;
import static java.util.stream.Collectors.groupingBy;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

import com.google.common.base.Preconditions;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Streams;
import com.google.inject.Key;
import com.google.inject.Provider;
import com.google.inject.util.Providers;
import com.kaching.entities.ApiRequestMetadata;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.queryengine.Tracer.TracerCall;
import com.kaching.platform.queryengine.authorization.EncryptedRequestAuthorizationToken;
import com.kaching.platform.queryengine.postprocessors.IdentityPostProcessor;
import com.kaching.platform.queryengine.progress.ProgressMonitor;
import com.kaching.platform.queryengine.progress.ProgressMonitor.StageProgress;
import com.kaching.platform.queryengine.xray.NoOpXrayRecorder;
import com.kaching.platform.queryengine.xray.XrayTracer;
import com.kaching.util.UncheckedInterruptedException;

public class DelegatingScheduledQueryExecutorService
    implements ScheduledQueryExecutorService {

  private static final Log log = getLog(DelegatingScheduledQueryExecutorService.class);
  private static final Map<Key<?>, Object> EMPTY = newHashMap();

  private final ExecutorService executorService;
  private final QueryDriver driver;
  private final QueryProxies queryProxies;
  private final Provider<Trace> traceProvider;
  private final Provider<Tracer> tracerProvider;
  private final Provider<Option<EncryptedRequestAuthorizationToken>> tokenProvider;
  private final Provider<ProgressMonitor> progressMonitorProvider;
  private final Provider<CallDepthCount> callDepthCountProvider;
  private final Provider<Option<OriginInfo>> originInfoProvider;
  private final Provider<Option<ApiRequestMetadata>> apiRequestMetadataProvider;
  private final XrayTracer xray;

  public DelegatingScheduledQueryExecutorService(
      ExecutorService executorService,
      QueryDriver driver,
      QueryProxies queryProxies,
      Provider<Trace> traceProvider,
      Provider<Tracer> tracerProvider,
      Provider<Option<EncryptedRequestAuthorizationToken>> tokenProvider,
      Provider<ProgressMonitor> progressMonitorProvider,
      Provider<CallDepthCount> callDepthCountProvider,
      Provider<Option<OriginInfo>> originInfoProvider,
      Provider<Option<ApiRequestMetadata>> apiRequestMetadataProvider) {
    this(executorService,
        driver,
        queryProxies,
        traceProvider,
        tracerProvider,
        tokenProvider,
        progressMonitorProvider,
        callDepthCountProvider,
        originInfoProvider,
        apiRequestMetadataProvider,
        new XrayTracer(new NoOpXrayRecorder()));
  }

  public DelegatingScheduledQueryExecutorService(
      ExecutorService executorService,
      QueryDriver driver,
      QueryProxies queryProxies,
      Provider<Trace> traceProvider,
      Provider<Tracer> tracerProvider,
      Provider<Option<EncryptedRequestAuthorizationToken>> tokenProvider,
      Provider<ProgressMonitor> progressMonitorProvider,
      Provider<CallDepthCount> callDepthCountProvider,
      Provider<Option<OriginInfo>> originInfoProvider,
      Provider<Option<ApiRequestMetadata>> apiRequestMetadataProvider,
      XrayTracer xray) {
    this.executorService = executorService;
    this.driver = driver;
    this.queryProxies = queryProxies;
    this.traceProvider = traceProvider;
    this.tracerProvider = tracerProvider;
    this.tokenProvider = tokenProvider;
    this.progressMonitorProvider = progressMonitorProvider;
    this.callDepthCountProvider = callDepthCountProvider;
    this.originInfoProvider = originInfoProvider;
    this.apiRequestMetadataProvider = apiRequestMetadataProvider;
    this.xray = xray;
  }

  @Override
  public void shutdown() {
    executorService.shutdown();
  }

  @Override
  public boolean isShutdown() {
    return executorService.isShutdown();
  }

  @Override
  public boolean isTerminated() {
    return executorService.isTerminated();
  }

  @Override
  public void execute(final Query<?> query) {
    // tracer() and token() must be called in the current thread (i.e. outside the runnable)
    tracer().add(new TracerCall(localRequest(query), 0, 0L, "()"));
    Map<Key<?>, Object> cache = queryCache(
        trace(),
        tracer(),
        token(),
        query.getClass(),
        callDepthCount(),
        originInfo(),
        apiRequestMetadata());
    xray.executeWithAsyncTracing(executorService).execute(() ->
        xray.subsegmentScope(query.getClass().getSimpleName()).openAndRun(() ->
            driver.execute(query, cache, Providers.of(new NullPostProcessor()))));
  }

  protected static Map<Key<?>, Object> queryCache(
      Trace trace,
      Tracer tracer,
      Option<EncryptedRequestAuthorizationToken> token,
      Class<? extends Query> queryClass,
      CallDepthCount callDepthCount,
      Option<OriginInfo> originInfo,
      Option<ApiRequestMetadata> apiRequestMetadata) {
    return new ImmutableMap.Builder<Key<?>, Object>() {{
      if (trace != null) {
        put(TRACE_KEY, trace);
      }
      if (tracer != null) {
        put(TRACER_KEY, tracer);
      }
      if (token != null) {
        put(REQUEST_AUTHORIZATION_TOKEN_KEY, token);
      }
      if (queryClass != null) {
        put(QUERY_CLASS_KEY, queryClass);
      }
      if (callDepthCount != null) {
        put(CALL_DEPTH_COUNT_KEY, callDepthCount);
      }
      if (originInfo != null) {
        put(ORIGIN_INFO_KEY, originInfo);
      }
      if (apiRequestMetadata != null) {
        put(API_REQUEST_METADATA_KEY, apiRequestMetadata);
      }
    }}.build();
  }

  @Override
  public void execute(Collection<? extends Query<?>> queries, int maxConcurrentQueries) {
    Preconditions.checkArgument(0 < maxConcurrentQueries);
    Map<String, StageProgress> queryClassProgress = initializeProgressMonitor(queries);
    final ConcurrentLinkedQueue<Query<?>> queue = new ConcurrentLinkedQueue<>(queries);
    // token() must be called in the current thread (i.e. outside the runnable)
    Trace trace = trace();
    Tracer tracer = tracer();
    Option<EncryptedRequestAuthorizationToken> token = token();
    CallDepthCount callDepthCount = callDepthCount();
    Option<OriginInfo> originInfo = originInfo();
    Option<ApiRequestMetadata> apiRequestMetadata = apiRequestMetadata();
    ExecutorService service = xray.executeWithAsyncTracing(executorService);
    for (int i = 0; i < maxConcurrentQueries; i++) {
      service.execute(() -> {
        Query<?> nextQuery = queue.poll();
        while (nextQuery != null) {
          if (Thread.interrupted()) {
            break;
          }
          try {
            final Query<?> query = nextQuery;
            xray.subsegmentScope(nextQuery.getClass().getSimpleName()).openAndRun(() -> {
              Map<Key<?>, Object> cache = queryCache(
                  trace,
                  tracer,
                  token,
                  query.getClass(),
                  callDepthCount,
                  originInfo,
                  apiRequestMetadata);
              driver.execute(query, cache, Providers.of(new NullPostProcessor()));
            });
          } catch (Exception e) {
            // Purposely ignoring this top level exception. It is the
            // responsibility of the driver to driver to log the error.
            if (e instanceof UncheckedInterruptedException) {
              break;
            }
          }
          queryClassProgress.get(getClassName(nextQuery)).incrementProgress(1);
          nextQuery = queue.poll();
        }
      });
    }
  }

  @Override
  public <T> CompletableFuture<T> submit(final Query<T> query) {
    // tracer() and token() must be called in the current thread (i.e. outside the runnable)
    final Tracer tracer = tracer();
    final Tracer delegate = tracer.newInstance();
    Map<Key<?>, Object> cache = queryCache(
        trace(),
        delegate,
        token(),
        query.getClass(),
        callDepthCount(),
        originInfo(),
        apiRequestMetadata());
    final CompletableFuture<T> future = CompletableFuture.supplyAsync(() ->
            xray.subsegmentScope(query.getClass().getSimpleName()).openAndSupply(() ->
                (T) driver.execute(query, cache, Providers.of(new IdentityPostProcessor()))),
        xray.executeWithAsyncTracing(executorService));

    if (!tracer.shouldTrace()) {
      return future;
    } else {
      final long start = System.currentTimeMillis();
      return future.whenComplete((result, exception) -> {
        int status = exception == null ? 200 : 500;
        tracer.add(
            new TracerCall(
                localRequest(query),
                status, System.currentTimeMillis() - start, delegate.getTrace()));
      });
    }
  }

  private Trace trace() {
    return getOrDefault(traceProvider, Trace.newTrace());
  }

  private Tracer tracer() {
    return getOrDefault(tracerProvider, Tracers.empty());
  }

  private Option<EncryptedRequestAuthorizationToken> token() {
    return getOrDefault(tokenProvider, Option.none());
  }

  private CallDepthCount callDepthCount() {
    return getOrDefault(callDepthCountProvider, new CallDepthCount(0L));
  }

  private Option<OriginInfo> originInfo() {
    return getOrDefault(originInfoProvider, Option.none());
  }

  private Option<ApiRequestMetadata> apiRequestMetadata() {
    return getOrDefault(apiRequestMetadataProvider, Option.none());
  }

  private <T> T getOrDefault(Provider<T> provider, T defaultValue) {
    return (provider == null || provider.get() == null) ? defaultValue : provider.get();
  }

  @Override
  public void scheduleWithFixedDelay(
      final Class<? extends Query<?>> queryClass,
      long initialDelay, long delay, TimeUnit unit) {
    Timer timer = new Timer("Timer " + queryClass.getName(), true);
    timer.scheduleAtFixedRate(new TimerTask() {
      @Override
      public void run() {
        Query<?> query = queryProxies.getProxy(queryClass).create();
        try {
          driver.execute(query, EMPTY, Providers.of(new IdentityPostProcessor()));
        } catch (Throwable t) {
          log.error(t);
        }
      }
    }, unit.toMillis(initialDelay), unit.toMillis(delay));
  }

  public ExecutorService delegate() {
    return executorService;
  }

  @Override
  public <T> T submitAndGetResult(Query<T> query) {
    return getOrFail(submit(query));
  }

  @Override
  public <Q extends Query<T>, T> List<QueryResult<Q, T>> submitAndGetResults(
      Iterable<? extends Q> queries,
      int maxConcurrentQueries) {
    List<? extends Q> queryList = Lists.newArrayList(queries);
    Preconditions.checkArgument(0 < maxConcurrentQueries);
    Map<String, StageProgress> queryClassProgress = initializeProgressMonitor(queries);
    final ConcurrentLinkedQueue<Q> queue = new ConcurrentLinkedQueue<>(queryList);
    // token() must be called in the current thread (i.e. outside the runnable)
    Trace trace = trace();
    Tracer tracer = tracer();
    Option<EncryptedRequestAuthorizationToken> token = token();
    CallDepthCount callDepthCount = callDepthCount();
    Option<OriginInfo> originInfo = originInfo();
    Option<ApiRequestMetadata> apiRequestMetadata = apiRequestMetadata();
    final List<QueryResult<Q, T>> results = synchronizedList(new ArrayList<>(queryList.size()));
    final List<Future<?>> futures = new ArrayList<>(maxConcurrentQueries);
    ExecutorService service = xray.executeWithAsyncTracing(executorService);
    for (int i = 0; i < maxConcurrentQueries; i++) {
      futures.add(service.submit(() -> {
        Q nextQuery = queue.poll();
        while (nextQuery != null) {
          final Q query = nextQuery;
          if (Thread.interrupted()) {
            break;
          }
          try {
            xray.subsegmentScope(query.getClass().getSimpleName()).openAndRun(() -> {
              Map<Key<?>, Object> cache = queryCache(
                  trace,
                  tracer,
                  token,
                  query.getClass(),
                  callDepthCount,
                  originInfo,
                  apiRequestMetadata);
              T result = (T) driver.execute(query, cache, Providers.of(new IdentityPostProcessor()));
              results.add(QueryResult.success(query, result));
            });
          } catch (Exception e) {
            // Purposely ignoring this top level exception. It is the
            // responsibility of the driver to log the error.
            results.add(QueryResult.error(nextQuery, e));
            if (e instanceof UncheckedInterruptedException) {
              break;
            }
          }
          queryClassProgress.get(getClassName(nextQuery)).incrementProgress(1);
          nextQuery = queue.poll();
        }
      }));
    }

    for (Future<?> future : futures) {
      try {
        getOrFail(future);
      } catch (UncheckedInterruptedException e) {
        queue.clear();
        futures.forEach(f -> f.cancel(true));
        throw e;
      }
    }

    Q query;
    while ((query = queue.poll()) != null) {
      results.add(QueryResult.success(query, null));
    }

    return results;
  }

  @Override
  public <Q extends Query<T>, T> List<Future<List<QueryResult<Q, T>>>> submitAndGetFutures(
      Iterable<? extends Q> queries,
      int maxConcurrentQueries) {
    List<? extends Q> queryList = Lists.newArrayList(queries);
    Preconditions.checkArgument(0 < maxConcurrentQueries);
    Map<String, StageProgress> queryClassProgress = initializeProgressMonitor(queries);
    ConcurrentLinkedQueue<Q> queue = new ConcurrentLinkedQueue<>(queryList);
    // token() must be called in the current thread (i.e. outside the runnable)
    Trace trace = trace();
    Tracer tracer = tracer();
    Option<EncryptedRequestAuthorizationToken> token = token();
    CallDepthCount callDepthCount = callDepthCount();
    Option<OriginInfo> originInfo = originInfo();
    Option<ApiRequestMetadata> apiRequestMetadata = apiRequestMetadata();
    List<Future<List<QueryResult<Q, T>>>> futures = new ArrayList<>(maxConcurrentQueries);
    ExecutorService service = xray.executeWithAsyncTracing(executorService);
    for (int i = 0; i < maxConcurrentQueries; i++) {
      futures.add(service.submit(() -> {
        Q nextQuery = queue.poll();
        List<QueryResult<Q, T>> queryResults = new ArrayList<>();
        while (nextQuery != null) {
          final Q query = nextQuery;
          if (Thread.interrupted()) {
            break;
          }
          QueryResult<Q, T> queryResult;
          try {
            queryResult = xray.subsegmentScope(query.getClass().getSimpleName()).openAndSupply(() -> {
              Map<Key<?>, Object> cache = queryCache(
                  trace,
                  tracer,
                  token,
                  query.getClass(),
                  callDepthCount,
                  originInfo,
                  apiRequestMetadata);
              T result = (T) driver.execute(query, cache, Providers.of(new IdentityPostProcessor()));
              return QueryResult.success(query, result);
            });
          } catch (Exception e) {
            // Purposely ignoring this top level exception. It is the
            // responsibility of the driver to log the error.
            queryResult = QueryResult.error(nextQuery, e);
            if (e instanceof UncheckedInterruptedException) {
              break;
            }
          }
          queryClassProgress.get(getClassName(nextQuery)).incrementProgress(1);
          nextQuery = queue.poll();
          queryResults.add(queryResult);
        }
        return queryResults;
      }));
    }
    return futures;
  }

  @Override
  public <Q extends Query<T>, T, K> List<QueryKeyResult<K, Q, T>> submitAndGetResultsWithKey(
      Map<K, ? extends Q> queriesMap,
      int maxConcurrentQueries) {
    Preconditions.checkArgument(0 < maxConcurrentQueries);
    Map<String, StageProgress> queryClassProgress = initializeProgressMonitor(queriesMap.values());
    final ConcurrentLinkedQueue<Map.Entry<K, ? extends Q>> queue = new ConcurrentLinkedQueue<>(queriesMap.entrySet());
    // token() must be called in the current thread (i.e. outside the runnable)
    Trace trace = trace();
    Tracer tracer = tracer();
    Option<EncryptedRequestAuthorizationToken> token = token();
    CallDepthCount callDepthCount = callDepthCount();
    Option<OriginInfo> originInfo = originInfo();
    Option<ApiRequestMetadata> apiRequestMetadata = apiRequestMetadata();
    final List<QueryKeyResult<K, Q, T>> results = synchronizedList(new ArrayList<>(queriesMap.size()));
    final List<Future<?>> futures = new ArrayList<>(maxConcurrentQueries);
    ExecutorService service = xray.executeWithAsyncTracing(executorService);
    for (int i = 0; i < maxConcurrentQueries; i++) {
      futures.add(service.submit(() -> {
        Map.Entry<K, ? extends Q> nextQuery = queue.poll();
        while (nextQuery != null) {
          if (Thread.interrupted()) {
            break;
          }
          final Map.Entry<K, ? extends Q> query = nextQuery;
          try {
            T queryResult = xray.subsegmentScope(query.getValue().getClass().getSimpleName()).openAndSupply(() -> {
              Map<Key<?>, Object> cache = queryCache(
                  trace,
                  tracer,
                  token,
                  query.getValue().getClass(),
                  callDepthCount,
                  originInfo,
                  apiRequestMetadata);
              T result = (T) driver.execute(query.getValue(), cache, Providers.of(new IdentityPostProcessor()));
              return result;
            });
            results.add(
                QueryKeyResult.success(nextQuery.getKey(), (Class<Q>) nextQuery.getValue().getClass(), queryResult));
          } catch (Exception e) {
            // Purposely ignoring this top level exception. It is the
            // responsibility of the driver to log the error.
            results.add(QueryKeyResult.error(nextQuery.getKey(), (Class<Q>) nextQuery.getValue().getClass(), e));
            if (e instanceof UncheckedInterruptedException) {
              break;
            }
          }
          queryClassProgress.get(getClassName(nextQuery.getValue())).incrementProgress(1);
          nextQuery = queue.poll();
        }
      }));
    }

    for (Future<?> future : futures) {
      try {
        getOrFail(future);
      } catch (UncheckedInterruptedException e) {
        queue.clear();
        futures.forEach(f -> f.cancel(true));
        throw e;
      }
    }

    Map.Entry<K, ? extends Q> query;
    while ((query = queue.poll()) != null) {
      results.add(QueryKeyResult.success(query.getKey(), (Class<Q>) query.getValue().getClass(), null));
    }

    return results;
  }

  @Override
  public void submitAndWaitForCompletion(Iterable<? extends Query> queries, int maxConcurrentQueries) {
    int i = 0;
    Map<Integer, Query> queryMap = new HashMap<>();
    for (Query query : queries) {
      queryMap.put(i++, query);
    }
    submitAndGetResultsWithKey(queryMap, maxConcurrentQueries);
  }

  private Map<String, StageProgress> initializeProgressMonitor(Iterable<? extends Query<?>> queries) {
    ProgressMonitor progressMonitor = progressMonitorProvider.get();
    Map<String, StageProgress> queryClassProgress = new HashMap<>();
    Streams.stream(queries)
        .collect(groupingBy(this::getClassName))
        .forEach((queryClassName, queryGroup) ->
            queryClassProgress.put(queryClassName, progressMonitor.startNewStage(queryClassName, queryGroup.size()))
        );
    return queryClassProgress;
  }

  private String getClassName(Query<?> query) {
    String[] split = query.getClass().getName().split("\\.");
    return split[split.length - 1];
  }

}
