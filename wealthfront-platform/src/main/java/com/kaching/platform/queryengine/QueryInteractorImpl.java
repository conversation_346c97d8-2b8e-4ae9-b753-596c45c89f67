package com.kaching.platform.queryengine;

import static com.google.common.collect.Multimaps.synchronizedListMultimap;
import static java.util.concurrent.TimeUnit.MILLISECONDS;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;

import org.joda.time.DateTime;
import org.joda.time.Duration;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.ListMultimap;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.kaching.platform.common.Option;
import com.kaching.platform.discovery.ServiceDescriptor;
import com.kaching.platform.functional.Unchecked;
import com.kaching.util.UncheckedInterruptedException;
import com.kaching.util.mail.Pager;

@Singleton
public class QueryInteractorImpl implements QueryInteractor, QueryInteractorListener {

  @VisibleForTesting static final String UPDATE_TIMEOUT_ACTION_NAME_INTERNAL = "[update timeout]";
  @VisibleForTesting static final String TIMEOUT_ACTION_NAME = "timeout";

  @VisibleForTesting final Map<QuerySessionId, BlockingQueue<String>> listeners = new ConcurrentHashMap<>();
  @VisibleForTesting final Map<QuerySessionId, DateTime> timeouts = new ConcurrentHashMap<>();
  @VisibleForTesting final ListMultimap<QuerySessionId, String> allowedRequests =
      synchronizedListMultimap(ArrayListMultimap.create());

  @Inject Provider<QuerySessionId> sessionIdProvider;
  @Inject Provider<DateTime> clock;
  @Inject Pager pager;
  @Inject ServiceDescriptor serviceDescriptor;

  static final Set<String> defaultActions = ImmutableSet.of(
      "kill-query",
      "snooze-minutes"
  );

  @Override
  public <T> T listenExpression(Duration timeout, Map<String, Supplier<T>> actions) {
    BlockingQueue<String> listenForRequest = createListenerQueue();
    QuerySessionId sessionId = sessionIdProvider.get();
    allowedRequests.putAll(sessionId, actions.keySet());
    allowedRequests.putAll(sessionId, defaultActions);
    listeners.put(sessionId, listenForRequest);
    timeouts.put(sessionId, clock.get().plus(timeout));
    try {
      while (true) {
        String request;
        long timeoutMillis = new Duration(clock.get(), timeouts.get(sessionId)).getMillis();
        request = Unchecked.get(() -> listenForRequest.poll(timeoutMillis, MILLISECONDS));
        if (request == null) {
          if (actions.containsKey(TIMEOUT_ACTION_NAME)) {
            return actions.get(TIMEOUT_ACTION_NAME).get();
          } else {
            throw new IllegalArgumentException("Query doesn't support the '"
                + TIMEOUT_ACTION_NAME + "' action");
          }
        }
        switch (request) {
          case "kill-query":
            throw new UncheckedInterruptedException();
          case UPDATE_TIMEOUT_ACTION_NAME_INTERNAL:
            continue;
          default:
            return actions.get(request).get();
        }
      }
    } finally {
      allowedRequests.removeAll(sessionId);
      listeners.remove(sessionId);
      timeouts.remove(sessionId);
    }
  }

  @Override
  public <T> T alertAndListenExpression(
      Pager.Device device, String alertTitle, Duration timeout, Map<String, Supplier<T>> actions) {
    pager.alert(
        alertTitle,
        "Respond using `ikq " + serviceDescriptor.getId()
            + " " + RequestQueryAction.class.getSimpleName()
            + " " + sessionIdProvider.get()
            + " [action]`. Available actions are: " + String.join(", ", actions.keySet())
            + ", kill-query, snooze-minutes [minutes]."
            + " Query will perform its default action after " + timeout.getStandardMinutes()
            + " minutes without response.",
        device);
    return listenExpression(timeout, actions);
  }

  @Override
  public String sendRequest(QuerySessionId sessionId, String request, Option<Integer> newTimeout) {
    BlockingQueue<String> listener = listeners.get(sessionId);
    if (listener == null) {
      return "Error: Query " + sessionId + " was not waiting for a request";
    }
    if (!allowedRequests.get(sessionId).contains(request)) {
      return "Error: Query " + sessionId + " is not expecting '" + request + "'. Allowed actions are "
          + allowedRequests.get(sessionId);
    }
    if ("snooze-minutes".equals(request)) {
      timeouts.put(sessionId, clock.get().plus(Duration.standardMinutes(newTimeout.getOrThrow())));
      listener.add(UPDATE_TIMEOUT_ACTION_NAME_INTERNAL);
      return "Snoozed Query " + sessionId + " until " + newTimeout.getOrThrow() + " minutes from now.";
    }
    listener.add(request);
    return "Sent request to Query " + sessionId + ".";
  }

  @VisibleForTesting
  BlockingQueue<String> createListenerQueue() {
    return new ArrayBlockingQueue<>(20);
  }

}
