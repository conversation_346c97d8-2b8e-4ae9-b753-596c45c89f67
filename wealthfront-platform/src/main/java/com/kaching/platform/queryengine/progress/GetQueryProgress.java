package com.kaching.platform.queryengine.progress;

import java.util.List;

import com.google.inject.Inject;
import com.kaching.platform.common.Strings;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.QueryRuntimeMonitor;
import com.kaching.platform.queryengine.QuerySession;
import com.kaching.platform.queryengine.admin.Param;

public class GetQueryProgress extends AbstractQuery<String> {

  private final String queryNameOrSessionId;

  public GetQueryProgress(@Param("Query name or session id") String queryNameOrSessionId) {
    this.queryNameOrSessionId = queryNameOrSessionId;
  }

  @Inject QueryRuntimeMonitor runtimeMonitor;
  @Inject QueryProgressFormatter formatter;

  @Override
  public String process() {
    List<QuerySession> matchingQuerySessions = runtimeMonitor.getQuerySessions(queryNameOrSessionId);

    if (matchingQuerySessions.isEmpty()) {
      return Strings.format("No running queries matched '%s'.", queryNameOrSessionId);
    } else {
      return formatter.formatAllQueryProgress(matchingQuerySessions);
    }
  }

}
