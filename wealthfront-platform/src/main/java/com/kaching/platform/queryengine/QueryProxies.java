package com.kaching.platform.queryengine;

import static com.google.common.collect.Iterators.unmodifiableIterator;
import static com.kaching.platform.common.logging.Log.getLog;

import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.kaching.JsonMarshallerFactory;
import com.kaching.platform.common.Strings;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.queryengine.exceptions.QueryNotFoundException;

public class QueryProxies implements Iterable<QueryProxy<?>> {

  private static final Log log = getLog(QueryProxies.class);

  private final Map<String, QueryProxy<? extends Query<?>>> queryProxies =
      new ConcurrentHashMap<>();

  private final JsonMarshallerFactory jsonMarshallerFactory;
  private final QueryRuntimeMonitor runtimeMonitor;

  public QueryProxies(JsonMarshallerFactory jsonMarshallerFactory, QueryRuntimeMonitor runtimeMonitor) {
    this.jsonMarshallerFactory = jsonMarshallerFactory;
    this.runtimeMonitor = runtimeMonitor;
  }

  /**
   * Registers query {@code Q}. A unique {@link QueryProxy} for {@code Q}
   * is created.
   */
  public <Q extends Query<?>> QueryProxies register(Class<Q> queryClass) {
    String queryName = queryClass.getSimpleName();
    if (queryProxies.get(queryName) == null) {
      synchronized (queryProxies) {
        if (queryProxies.get(queryName) == null) {
          QueryProxy<Q> queryProxy = new QueryProxy<>(queryClass, jsonMarshallerFactory);
          log.debug("registering query: " + queryName);
          queryProxies.put(queryName, queryProxy);
        }
      }
    }
    return this;
  }

  @SuppressWarnings("unchecked")
  public <Q extends Query<?>> QueryProxy<Q> getProxy(String name) {
    QueryProxy<? extends Query<?>> queryProxy = queryProxies.get(name);
    if (queryProxy == null) {
      QueryNotFoundException exception = new QueryNotFoundException(Strings.format("Query with name '%s' not " +
          "found", name));
      runtimeMonitor.record(new MissingQuery(), 0, exception);
      throw exception;
    }
    return (QueryProxy<Q>) queryProxy;
  }

  public <Q extends Query<?>> QueryProxy<Q> getProxy(Class<Q> queryClass) {
    return new QueryProxy<>(queryClass, jsonMarshallerFactory);
  }

  @Override
  public Iterator<QueryProxy<?>> iterator() {
    return unmodifiableIterator(queryProxies.values().iterator());
  }

}
