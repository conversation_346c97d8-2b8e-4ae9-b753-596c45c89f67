package com.kaching.platform.queryengine.predicates;

import org.joda.time.DateTime;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.kaching.platform.queryengine.Predicate;
import com.wealthfront.entities.Market;

/**
 * A predicate which is satisfied when the market is opened.
 */
public class MarketOpenPredicate implements Predicate {

  @Inject Market market;
  @Inject Provider<DateTime> clock;

  public boolean satisfied() {
    return market.isOpen(clock.get());
  }

}
