package com.kaching.platform.queryengine.xray;

import com.google.common.annotations.VisibleForTesting;
import com.google.inject.Injector;
import com.google.inject.Provider;
import com.kaching.platform.queryengine.DelegatingScopedQueryDriver;
import com.kaching.platform.queryengine.PostProcessor;
import com.kaching.platform.queryengine.Query;
import com.kaching.platform.queryengine.ScopedQueryDriver;
import com.kaching.platform.queryengine.xray.XrayTracingQueryEngineModule.XrayTracingFeature;

public class XrayTracingQueryDriver extends DelegatingScopedQueryDriver {

  private final Injector injector;

  public XrayTracingQueryDriver(Injector injector, ScopedQueryDriver delegate) {
    super(delegate);
    this.injector = injector;
  }

  @Override
  public <T> Object execute(Query<T> query, Provider<? extends PostProcessor> postProcessor) {
    XrayTracingFeature feature = injector.getInstance(XrayTracingFeature.class);
    if (feature.isTracingEnabled()) {
      return executeTraced(query, postProcessor);
    } else {
      return executeUntraced(query, postProcessor);
    }
  }

  @VisibleForTesting
  <T> Object executeTraced(Query<T> query, Provider<? extends PostProcessor> postProcessor) {
    QueryDriverSegmentTracer tracer = injector.getInstance(QueryDriverSegmentTracer.class);
    return tracer.execute(query, () -> delegate.execute(query, postProcessor));
  }

  @VisibleForTesting
  <T> Object executeUntraced(Query<T> query, Provider<? extends PostProcessor> postProcessor) {
    return delegate.execute(query, postProcessor);
  }

}
