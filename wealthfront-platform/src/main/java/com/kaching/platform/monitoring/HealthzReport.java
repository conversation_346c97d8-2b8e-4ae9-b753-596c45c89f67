package com.kaching.platform.monitoring;

import static com.google.common.collect.Lists.newArrayList;
import static com.google.common.collect.Maps.newHashMap;
import static com.kaching.DefaultKachingMarshallers.createMarshaller;

import java.util.List;
import java.util.Map;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.api.ExposeType.RewriteNamespace;
import com.kaching.platform.common.Strings;
import com.kaching.platform.queryengine.ServiceRevision;
import com.twolattes.json.Entity;
import com.twolattes.json.Marshaller;
import com.twolattes.json.Value;

/**
 * A healthz report gives an overview of the health of a server. In particular,
 * it includes the rolling statistics for all queries executed and the "hot"
 * stack traces.
 */
@Entity
@ExposeType(value = {ExposeTo.LOCAL, ExposeTo.BACKEND}, namespace = RewriteNamespace.DO_NOT_COPY)
public class HealthzReport {

  public static final Marshaller<HealthzReport> MARSHALLER = createMarshaller(HealthzReport.class);

  @Value
  private Map<String, Statistics> statistics = newHashMap();

  @Value
  private List<StackTraces> stackTraces = newArrayList();

  @Value(optional = true)
  private List<DownstreamHealthzReport> downstreamReports = newArrayList();

  /**
   * Gets the statistics. This method returns the actual map of the report
   * allowing modifications to the collection.
   */
  public Map<String, Statistics> getStatistics() {
    return statistics;
  }

  /**
   * Gets the stack traces. This method returns the actual list of stack traces
   * of the report allowing modifications to the collection.
   */
  public List<StackTraces> getStackTraces() {
    return stackTraces;
  }

  public List<DownstreamHealthzReport> getDownstreamReports() {
    return downstreamReports;
  }

  @Override
  public String toString() {
    return MARSHALLER.marshall(this).toString();
  }

  @Entity
  @ExposeType(value = {ExposeTo.LOCAL, ExposeTo.BACKEND}, namespace = RewriteNamespace.DO_NOT_COPY)
  public static class Statistics {

    @Value
    private double average;

    @Value
    private int median;

    @Value
    private int min;

    @Value
    private int max;

    @Value
    private int count;

    @Value
    private double failedQueries;

    @Value(optional = true)
    private double invalidQueries;

    /* JSON */
    @SuppressWarnings("unused")
    private Statistics() {}

    public Statistics(
        double average, int median, int min, int max, int count, double failedQueries) {
      this(average, median, min, max, count, failedQueries, 0.0);
    }

    public Statistics(
        double average, int median, int min, int max, int count, double failedQueries, double invalidQueries) {
      this.average = average;
      this.median = median;
      this.min = min;
      this.max = max;
      this.count = count;
      this.failedQueries = failedQueries;
      this.invalidQueries = invalidQueries;
    }

    public double getAverage() {
      return average;
    }

    public int getMedian() {
      return median;
    }

    public int getMin() {
      return min;
    }

    public int getMax() {
      return max;
    }

    public int getCount() {
      return count;
    }

    public double getFailedQueries() {
      return failedQueries;
    }

    public double getInvalidQueries() {
      return invalidQueries;
    }

    @Override
    public String toString() {
      return Strings.format("Avg: %s; Med: %s; Min: %s; Max: %s; Count: %s; Failed Queries: %s; Invalid Queries: %s",
          average, median, min, max, count, failedQueries, invalidQueries);
    }

  }

  @Entity
  @ExposeType(value = {ExposeTo.LOCAL, ExposeTo.BACKEND}, namespace = RewriteNamespace.DO_NOT_COPY)
  public static class StackTraces {

    @Value
    private String stackTrace;

    @Value
    private int count;

    /* JSON */
    @SuppressWarnings("unused")
    private StackTraces() {}

    public StackTraces(String stackTrace, int count) {
      this.stackTrace = stackTrace;
      this.count = count;
    }

    public String getStackTrace() {
      return stackTrace;
    }

    public int getCount() {
      return count;
    }

  }

  @Entity
  @ExposeType(value = {ExposeTo.LOCAL, ExposeTo.BACKEND}, namespace = RewriteNamespace.DO_NOT_COPY)
  public static class DownstreamHealthzReport {

    @Value
    private ServiceRevision serviceRevision;

    @Value
    private Map<String, Statistics> statistics = newHashMap();

    /* JSON */
    @SuppressWarnings("unused")
    private DownstreamHealthzReport() {}

    public DownstreamHealthzReport(ServiceRevision serviceRevision, Map<String, Statistics> statistics) {
      this.serviceRevision = serviceRevision;
      this.statistics = statistics;
    }

    public ServiceRevision getServiceRevision() {
      return serviceRevision;
    }

    public Map<String, Statistics> getStatistics() {
      return statistics;
    }

  }

}
