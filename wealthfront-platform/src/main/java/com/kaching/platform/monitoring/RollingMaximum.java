package com.kaching.platform.monitoring;

import java.util.LinkedList;
import java.util.concurrent.TimeUnit;

import org.joda.time.DateTime;

import com.google.inject.Provider;

public class RollingMaximum {

  private final long window;
  private final Provider<DateTime> clock;
  private final LinkedList<Event> queue;

  public RollingMaximum(long window, TimeUnit unit, Provider<DateTime> clock) {
    this.window = unit.toMillis(window);
    this.clock = clock;
    this.queue = new LinkedList<>();
  }

  public synchronized void record(int value) {
    long timestamp = clock.get().getMillis() + window;
    while (queue.peekLast() != null && queue.peekLast().value <= value) {
      queue.pollLast();
    }
    queue.add(new Event(timestamp, value));
  }

  public synchronized int getRollingMaximum() {
    purge();
    if (queue.isEmpty()) {
      return 0;
    }
    int max = Integer.MIN_VALUE;
    for (Event event : queue) {
      if (max < event.value) {
        max = event.value;
      }
    }
    return max;
  }

  private void purge() {
    long now = clock.get().getMillis();
    while (queue.peekFirst() != null && queue.peekFirst().timestamp < now) {
      queue.pollFirst();
    }
  }

  static class Event {

    private final long timestamp;
    private final int value;

    Event(long timestamp, int value) {
      this.timestamp = timestamp;
      this.value = value;
    }
  }
}
