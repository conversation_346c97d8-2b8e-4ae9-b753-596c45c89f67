package com.kaching.platform.hibernate.queue.impl;

import static com.google.common.base.Preconditions.checkArgument;
import static com.google.common.collect.ImmutableMap.toImmutableMap;
import static com.google.common.collect.ImmutableSortedMap.toImmutableSortedMap;
import static com.wealthfront.util.stream.WFCollectors.entriesToMap;
import static com.wealthfront.util.stream.WFCollectors.toMapWithKey;
import static java.util.Collections.emptyList;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.LongAdder;

import org.joda.time.DateTime;
import org.joda.time.Duration;

import com.google.common.collect.ContiguousSet;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSortedMap;
import com.google.common.collect.Range;
import com.google.common.collect.RangeSet;
import com.google.common.collect.Sets;
import com.google.common.collect.TreeRangeSet;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.kaching.Author;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Pair;
import com.kaching.platform.common.Strings;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.discovery.ServiceDescriptor;
import com.kaching.platform.functional.Unit;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.RetryingTransacter;
import com.kaching.platform.hibernate.WithReadOnlySession;
import com.kaching.platform.hibernate.WithSession;
import com.kaching.platform.hibernate.WithSessionExpression;
import com.kaching.platform.hibernate.queue.BatchItemId;
import com.kaching.platform.hibernate.queue.BatchItemOutcome;
import com.kaching.platform.hibernate.queue.BatchQueue;
import com.kaching.platform.hibernate.queue.BatchQueueThrottler;
import com.kaching.platform.hibernate.queue.BatchSizer;
import com.kaching.platform.hibernate.queue.impl.BatchQueueData.BatchItem;
import com.kaching.platform.hibernate.queue.impl.BatchQueueData.DeflatedBatch;
import com.kaching.platform.hibernate.queue.impl.BatchQueueData.InflatedBatch;
import com.kaching.platform.hibernate.queue.impl.BatchQueueData.RetryBatch;
import com.kaching.platform.monitoring.RuntimeVarzValue;
import com.kaching.platform.queryengine.StackTraceMonitor;
import com.kaching.platform.util.Ranges;
import com.kaching.util.pagerduty.AuthorToPagerDuty;
import com.kaching.util.time.NanoTimeProvider;
import com.twolattes.json.Json;
import com.twolattes.json.Marshaller;

class BatchQueueProcessorImpl implements BatchQueueProcessor {
  
  private static final Log log = Log.getLog(BatchQueueProcessorImpl.class);
  private static final long UNEXPECTED_EXCEPTION_DELAY_MILLIS = 60 * 1000;
  
  private final LongAdder numItemsAttempted = new LongAdder();
  private final LongAdder numBatchesAttempted = new LongAdder();
  private final LongAdder numRetryableExceptions = new LongAdder();
  private final LongAdder numErrorExceptions = new LongAdder();
  private final LongAdder numBatchSplits = new LongAdder();
  
  @Inject RetryingTransacter transacter;
  @Inject Provider<DateTime> clock;
  @Inject ServiceDescriptor serviceDescriptor;
  @Inject StackTraceMonitor stm;
  @Inject NanoTimeProvider nanoTimeProvider;

  @Override
  public <B, I> InflationResult<B, I> inflateBatches(Marshaller<B> batchDataMarshaller, Marshaller<I> itemDataMarshaller, List<DeflatedBatch> batches) {
    try {
      List<InflatedBatch<B, I>> successful = new ArrayList<>(batches.size());
      Map<Id<BatchQueueBatch>, Exception> errors = new HashMap<>();
      transacter.execute(new WithReadOnlySession() {

        @Inject BatchQueueBatchRepository batchRepository;
        @Inject BatchQueueItemRepository itemRepository;

        @Override
        public void run(DbSession session) {
          successful.clear();
          errors.clear();

          Map<Id<BatchQueueBatch>, BatchQueueBatch> entities = batchRepository.get(batches.stream().map(DeflatedBatch::getBatchId).collect(toList()))
              .stream()
              .collect(toMapWithKey(BatchQueueBatch::getId));

          RangeSet<Id<BatchQueueItem>> itemIds = TreeRangeSet.create();
          List<DeflatedBatch> canProcess = new ArrayList<>();
          for (DeflatedBatch batch : batches) {
            BatchQueueBatch entity = entities.get(batch.getBatchId());
            if (entity != null && canProcess(entity)) {
              canProcess.add(batch);
              itemIds.add(batch.getItemIdRange());
            }
          }
          Map<Id<BatchQueueItem>, Json.Value> payloads = itemRepository.getPayloads(itemIds);
          for (DeflatedBatch batch : canProcess) {
            BatchQueueBatch entity = entities.get(batch.getBatchId());
            try {
              List<BatchItem<I>> items = new ArrayList<>(batch.getSize());
              for (Id<BatchQueueItem> itemId : ContiguousSet.create(batch.getItemIdRange(), Ranges.getIdDiscreteDomain())) {
                if (payloads.containsKey(itemId)) {
                  items.add(new BatchItem<>(itemId, itemDataMarshaller.unmarshall(payloads.get(itemId))));
                } else {
                  log.error("Missing payload for item ID: " + itemId);
                  break;
                }
              }
              if (items.size() != batch.getSize()) {
                log.error("Missing payloads for batch ID: " + batch.getBatchId());
                errors.put(batch.getBatchId(), new IllegalStateException("Missing items"));
              } else {
                B batchData = batchDataMarshaller.unmarshall(entity.getBatchData());
                successful.add(new InflatedBatch<>(batch.getBatchId(), batchData, entity.getBatchData(), items, batch.getTryNumber()));
              }
            } catch (Exception e) {
              log.error(e, "Exception deserializing batch %s", batch.getBatchId());
              errors.put(batch.getBatchId(), e);
            }
          }
        }
      });
      if (!errors.isEmpty()) {
        errors.values().stream().limit(1).forEach(stm::add);
        transacter.execute(new WithSession() {
          @Inject BatchQueueBatchRepository batchRepository;
          @Override
          public void run(DbSession session) {
            for (BatchQueueBatch batch : batchRepository.get(errors.keySet())) {
              Exception ex = errors.get(batch.getId());
              batch.markAsErrored(clock.get(), Strings.format("Error deserializing batch %s: %s", ex.getClass().getSimpleName(), ex.getMessage()));
            }
          }
        });
      }
      return new InflationResult<>(successful, null);
    } catch (RuntimeException ex) {
      Set<Id<BatchQueueBatch>> ids = batches.stream().map(DeflatedBatch::getBatchId).collect(toSet());
      log.error(ex, "Unexpected exception in readItemsForBatches. Failing IDs so they can be picked up again later: " + ids);
      return new InflationResult<>(emptyList(), ex);
    }
  }
  
  @Override
  public <B, I> ProcessResult processAndPersistResult(
      BatchQueue<B, I> queue,
      BatchSizer batchSizer,
      BatchQueueThrottler throttler,
      List<InflatedBatch<B, I>> inputs) {
    int targetBatchSize = batchSizer.getTargetBatchSize();
    Set<Json.Value> batchDatas = inputs.stream().map(InflatedBatch::getBatchDataSerialized).collect(toSet());
    if (batchDatas.size() != 1) {
      throw new IllegalArgumentException("All batches must have the same batch data: " + batchDatas);
    }
    int numItems = inputs.stream().mapToInt(batch -> batch.getItemsAsMap().size()).sum();
    numItemsAttempted.add(numItems);
    numBatchesAttempted.increment();
    ProcessResult output = new ProcessResult();
    List<InflatedBatch<B, I>> oversizedRemoved = inputs.stream()
        .filter(batch -> {
          if (batch.getItemsAsMap().size() > targetBatchSize) {
            output.add(splitOversizedBatch(targetBatchSize, batch));
            return false;
          }
          return true;
        }).collect(toList());
    if (!oversizedRemoved.isEmpty()) {
      output.add(processAllAtOnce(queue, batchSizer, throttler, oversizedRemoved));
    }
    return output;
  }

  @Override
  public List<RuntimeVarzValue> getVarz(String category) {
    return ImmutableList.of(
        new RuntimeVarzValue.CounterValue(category, "NumItemsAttempted", numItemsAttempted.sum()),
        new RuntimeVarzValue.CounterValue(category, "NumBatchesAttempted", numBatchesAttempted.sum()),
        new RuntimeVarzValue.CounterValue(category, "NumRetryableExceptions", numRetryableExceptions.sum()),
        new RuntimeVarzValue.CounterValue(category, "NumErrorExceptions", numErrorExceptions.sum()),
        new RuntimeVarzValue.CounterValue(category, "NumBatchSplits", numBatchSplits.sum())
    );
  }

  private <B, I> ProcessResult splitOversizedBatch(int batchSize, InflatedBatch<B, I> batch) {
    try {
      return transacter.executeWithSessionExpression(session -> {
        BatchQueueBatch parentBatch = session.getOrThrow(BatchQueueBatch.class, batch.getId());
        ProcessResult output = new ProcessResult();
        if (!canProcess(parentBatch)) {
          log.info("Parent batch %s is no longer processable", parentBatch.getId());
          return output;
        }
        for (ContiguousSet<Id<BatchQueueItem>> itemIds : Ranges.partition(parentBatch.getItemIds().range(), Ranges.getIdDiscreteDomain(), batchSize)) {
          Id<BatchQueueItem> fromId = itemIds.range().lowerEndpoint();
          Id<BatchQueueItem> toId = itemIds.range().upperEndpoint();
          Id<BatchQueueBatch> newBatchId;
          if (fromId.equals(parentBatch.getItemIdFrom())) {
            parentBatch.shrink(toId);
            newBatchId = parentBatch.getId();
          } else {
            BatchQueueBatch childBatch = new BatchQueueBatch(parentBatch.getQueueId(), fromId, toId, parentBatch.getCreatedAt(),
                parentBatch.getBatchData());
            childBatch.markAsPolled(clock.get(), serviceDescriptor.getId());
            newBatchId = session.save(childBatch);
            numBatchSplits.increment();
          }
          DeflatedBatch batch1 = new DeflatedBatch(newBatchId, fromId, toId, itemIds.size(), batch.getBatchDataSerialized(), batch.getTryNumber(), Option.none());
          output.addBatch(new RetryBatch(batch1, 0L));
        }
        return output;
      });
    } catch (RuntimeException ex) {
      log.error(ex, "Unable to split batch. Will re-process ID: " + batch.getId());
      stm.add(ex);
      DeflatedBatch batch1 = batch.deflate();
      return new ProcessResult()
          .addException(ex)
          .addBatch(new RetryBatch(batch1, UNEXPECTED_EXCEPTION_DELAY_MILLIS));
    }
  }
  
  private static class RowsToOutcomes<T> {
    
    private final Map<Id<BatchQueueItem>, Pair<T, BatchItemOutcome>> rows;

    RowsToOutcomes(Map<Id<BatchQueueItem>, Pair<T, BatchItemOutcome>> rows) {
      this.rows = rows;
    }
    
  } 

  private <B, I> ProcessResult processAllAtOnce(BatchQueue<B, I> queue,
                                                BatchSizer batchSizer,
                                                BatchQueueThrottler throttler,
                                                List<InflatedBatch<B, I>> batches) {
    int targetBatchSize = batchSizer.getTargetBatchSize();
    Map<Id<BatchQueueBatch>, InflatedBatch<B, I>> rowIdToBatch = batches.stream()
        .collect(toMapWithKey(InflatedBatch::getId));
    ImmutableSortedMap.Builder<Id<BatchQueueItem>, I> allItemsBuilder = ImmutableSortedMap.naturalOrder();
    for (InflatedBatch<B, I> batch : batches) {
      allItemsBuilder.putAll(batch.getItemsAsMap());
    }
    B batchData = batches.get(0).getBatchData();
    
    Map<Id<BatchQueueBatch>, Integer> batchSizes = rowIdToBatch.entrySet().stream()
        .map(entry -> Pair.of(entry.getKey(), entry.getValue().getItems().size()))
        .collect(toImmutableSortedMap(Comparator.naturalOrder(), Pair::getLeft, Pair::getRight));
    String description = Strings.format("%s | %s | BatchIds to Size: %s", queue.getQueueName(), 
        batches.get(0).getBatchDataSerialized().toPrettyPrintString(), batchSizes);

    long nanoStart = nanoTimeProvider.get();
    Pair<RowsToOutcomes<I>, Option<Exception>> outcomes1 = getOutcomes(queue, batchData, allItemsBuilder.build(), description);
    
    Duration processTime = Duration.millis((nanoTimeProvider.get() - nanoStart) / 1_000_000);
    int actualBatchSize = outcomes1.left.rows.size();
    
    Integer maxRetryBatchSize;
    Map<Id<BatchQueueBatch>, Duration> retryDelays = new HashMap<>();
    if (outcomes1.getRight().isDefined()) {
      Exception exception = outcomes1.getRight().getOrThrow();
      maxRetryBatchSize = safelyObserveFailedBatchAndGetNewSize(queue, batchSizer, targetBatchSize, actualBatchSize, processTime, exception).getOrNull();
      for (InflatedBatch<B, I> batch : batches) {
        retryDelays.put(batch.getId(), safelyObserveFailedBatchAndGetRetryDelay(queue, throttler, batch.getBatchDataSerialized(), batch.getTryNumber(), Option.some(exception)));
      }
    } else if (outcomes1.left.rows.values().stream().allMatch(pair -> pair.getRight() instanceof BatchItemOutcome.Success)) {
      safelyObserveSuccessfulBatch(queue, batchSizer, targetBatchSize, actualBatchSize, processTime);
      safelyObserveSuccessfulBatch(queue, throttler, batches.get(0).getBatchDataSerialized());
      maxRetryBatchSize = null;
    } else {
      for (InflatedBatch<B, I> batch : batches) {
        Set<Id<BatchQueueItem>> batchItemIds = Sets.union(batch.getItemsAsMap().keySet(), outcomes1.left.rows.keySet());
        if (batchItemIds.stream().anyMatch(id -> outcomes1.left.rows.get(id).getRight() instanceof BatchItemOutcome.Retry)) {
          retryDelays.put(batch.getId(), safelyObserveFailedBatchAndGetRetryDelay(queue, throttler, batch.getBatchDataSerialized(), batch.getTryNumber(), Option.none()));
        }
      }
      maxRetryBatchSize = null;
    }
    
    RowsToOutcomes<I> outcomes2 = setErrorFlagForExhaustedRetries(queue, batches, outcomes1);
    
    try {
      return transacter.execute(new WithSessionExpression<ProcessResult>() {

        @Inject BatchQueueBatchRepository repository;

        @Override
        public ProcessResult run(DbSession session) {
          List<BatchQueueBatch> entities = repository.get(rowIdToBatch.keySet());
          ProcessResult result = new ProcessResult();
          for (BatchQueueBatch entity : entities) {
            InflatedBatch<B, I> batchWithItems = rowIdToBatch.get(entity.getId());
            Set<Id<BatchQueueItem>> idsInBatch = batchWithItems.getItemsAsMap().keySet();
            Map<Id<BatchQueueItem>, Pair<I, BatchItemOutcome>> outcomesForEntity = outcomes2.rows.entrySet().stream()
                .filter(entry -> idsInBatch.contains(entry.getKey()))
                .collect(entriesToMap());
            Duration retryDelay = retryDelays.getOrDefault(entity.getId(), Duration.ZERO);
            result.add(handleOutcomeForSingleRow(session, entity, batchWithItems, outcomesForEntity, maxRetryBatchSize, retryDelay));
          }
          return result;
        }
      });
    } catch (RuntimeException ex) {
      log.error(ex, "Unable to record batch output. Will re-process. " + description);
      addToStm(queue, ex);
      ProcessResult output = new ProcessResult().addException(ex);
      batches.forEach(batch -> {
        DeflatedBatch batch1 = batch.deflate();
        output.addBatch(new RetryBatch(batch1, UNEXPECTED_EXCEPTION_DELAY_MILLIS));
      });
      return output;
    }
  }

  private <B, I> ProcessResult handleOutcomeForSingleRow(
      DbSession session,
      BatchQueueBatch oldRow,
      InflatedBatch<B, I> batchInput,
      Map<Id<BatchQueueItem>, Pair<I, BatchItemOutcome>> outcomes,
      Integer maxRetryBatchSize,
      Duration retryDelay
  ) {
    DateTime now = clock.get();
    List<Pair<BatchItemOutcome, ContiguousSet<Id<BatchQueueItem>>>> outcomeRuns = new ArrayList<>();
    for (Id<BatchQueueItem> itemId : ImmutableList.sortedCopyOf(batchInput.getItemsAsMap().keySet())) {
      BatchItemOutcome lastOutcome = outcomeRuns.isEmpty() ? null : outcomeRuns.get(outcomeRuns.size() - 1).getLeft();
      ContiguousSet<Id<BatchQueueItem>> lastSet = outcomeRuns.isEmpty() ? null : outcomeRuns.get(outcomeRuns.size() - 1).getRight();
      
      BatchItemOutcome thisOutcome = outcomes.get(itemId).getRight();
      if (lastOutcome != null && 
          lastOutcome.equals(thisOutcome) &&
          (maxRetryBatchSize == null || lastSet.size() < maxRetryBatchSize || !(lastOutcome instanceof BatchItemOutcome.Retry))) {
        ContiguousSet<Id<BatchQueueItem>> newSet = addToSet(outcomeRuns.get(outcomeRuns.size() - 1).getRight(), itemId);
        outcomeRuns.set(outcomeRuns.size() - 1, Pair.of(thisOutcome, newSet));
      } else {
        outcomeRuns.add(Pair.of(thisOutcome, ContiguousSet.create(Range.singleton(itemId), BatchQueueItem.DISCRETE_DOMAIN)));
      }
    }
    if (outcomeRuns.size() == 1) {
      return outcomeRuns.get(0).getLeft().visit(new BatchItemOutcome.Visitor<ProcessResult>() {
        @Override
        public ProcessResult caseSuccess(BatchItemOutcome.Success success) {
          oldRow.markAsSent(clock.get());
          return new ProcessResult();
        }

        @Override
        public ProcessResult caseRetry(BatchItemOutcome.Retry retry) {
          DeflatedBatch batch = batchInput.deflate().incrementTryNumber();
          if (maxRetryBatchSize != null) {
            batch = batch.setRetryBatchSize(maxRetryBatchSize);
          }
          return new ProcessResult().addBatch(new RetryBatch(batch, retryDelay.getMillis()));
        }

        @Override
        public ProcessResult caseErrorFlag(BatchItemOutcome.ErrorFlag error) {
          oldRow.markAsErrored(now, error.getMessage());
          return new ProcessResult();
        }
      });
    }
    ProcessResult batchOutput = new ProcessResult();
    for (Pair<BatchItemOutcome, ContiguousSet<Id<BatchQueueItem>>> pair : outcomeRuns) {
      ContiguousSet<Id<BatchQueueItem>> idSet = pair.getRight();
      BatchQueueBatch subBatch;
      if (idSet.first().equals(oldRow.getItemIdFrom())) {
        oldRow.shrink(idSet.last());
        subBatch = oldRow;
      } else {
        subBatch = new BatchQueueBatch(oldRow.getQueueId(), idSet.first(), idSet.last(), now, oldRow.getBatchData());
        subBatch.markAsPolled(oldRow.getPolledAt().getOrThrow(), serviceDescriptor.getId());
      }

      numBatchSplits.increment();
      pair.getLeft().visit(new BatchItemOutcome.Visitor<Id<BatchQueueBatch>>() {
        @Override
        public Id<BatchQueueBatch> caseSuccess(BatchItemOutcome.Success success) {
          subBatch.markAsSent(now);
          return session.save(subBatch);
        }

        @Override
        public Id<BatchQueueBatch> caseRetry(BatchItemOutcome.Retry retry) {
          Id<BatchQueueBatch> newChildId = session.save(subBatch);
          DeflatedBatch deflatedNew = new DeflatedBatch(newChildId, idSet.first(), idSet.last(),
              idSet.size(), batchInput.getBatchDataSerialized(), batchInput.getTryNumber() + 1, Option.of(maxRetryBatchSize));
          batchOutput.addBatch(new RetryBatch(deflatedNew, retryDelay.getMillis()));
          return newChildId;
        }

        @Override
        public Id<BatchQueueBatch> caseErrorFlag(BatchItemOutcome.ErrorFlag error) {
          subBatch.markAsErrored(now, error.getMessage());
          return session.save(subBatch);
        }
      });
    }
    return batchOutput;
  }

  private <B, I> Pair<RowsToOutcomes<I>, Option<Exception>> getOutcomes(BatchQueue<B, I> queue,
                                                                        B batchData,
                                                                        Map<Id<BatchQueueItem>, I> allItems,
                                                                        String batchDescription) {
    Map<BatchItemId, I> allItemsAsBatchItemIds = allItems.entrySet().stream()
        .collect(toImmutableMap(e -> new BatchItemId(Long.toString(e.getKey().getId())), Map.Entry::getValue));
    ImmutableSortedMap.Builder<Id<BatchQueueItem>, Pair<I, BatchItemOutcome>> result = ImmutableSortedMap.naturalOrder();
    BatchItemOutcome defaultOutcome = new BatchItemOutcome.ErrorFlag("Missing outcome");
    try {
      Map<BatchItemId, BatchItemOutcome> outcomes = queue.processBatch(batchData, allItemsAsBatchItemIds);
      for (Id<BatchQueueItem> itemId : allItems.keySet()) {
        BatchItemOutcome outcome = outcomes.getOrDefault(new BatchItemId(Long.toString(itemId.getId())), defaultOutcome);
        result.put(itemId, Pair.of(allItems.get(itemId), outcome));
      }
      return Pair.of(new RowsToOutcomes<>(result.build()), Option.none());
    } catch (Exception e) {
      log.error(e, batchDescription);
      try {
        BatchItemOutcome outcome = queue.handleProcessBatchException(e);
        outcome.visit(new BatchItemOutcome.Visitor<Unit>() {
          @Override
          public Unit caseSuccess(BatchItemOutcome.Success success) {
            return Unit.unit;
          }

          @Override
          public Unit caseRetry(BatchItemOutcome.Retry retry) {
            numRetryableExceptions.increment();
            return Unit.unit;
          }

          @Override
          public Unit caseErrorFlag(BatchItemOutcome.ErrorFlag error) {
            numErrorExceptions.increment();
            addToStm(queue, e);
            return Unit.unit;
          }
        });
        for (Id<BatchQueueItem> itemId : allItems.keySet()) {
          result.put(itemId, Pair.of(allItems.get(itemId), outcome));
        }
      } catch (Exception e2) {
        numErrorExceptions.increment();
        addToStm(queue, e2);
        log.error(e2, batchDescription);
        BatchItemOutcome.ErrorFlag outcome = new BatchItemOutcome.ErrorFlag("Exception in handleBatchException: " + e2);
        for (Id<BatchQueueItem> itemId : allItems.keySet()) {
          result.put(itemId, Pair.of(allItems.get(itemId), outcome));
        }
      }
      return Pair.of(new RowsToOutcomes<>(result.build()), Option.some(e));
    }
  }

  private <B, I> RowsToOutcomes<I> setErrorFlagForExhaustedRetries(BatchQueue<?, ?> queue,
                                                                   List<InflatedBatch<B, I>> batches,
                                                                   Pair<RowsToOutcomes<I>, Option<Exception>> outcomes) {
    Map<Id<BatchQueueItem>, InflatedBatch<B, I>> itemsToBatch = batches.stream()
        .flatMap(batch -> batch.getItemsAsMap().keySet().stream().map(item -> Pair.of(item, batch)))
        .collect(toMap(Pair::getLeft, Pair::getRight));
    Map<Integer, String> errorMessagesCache = new HashMap<>();

    ImmutableSortedMap.Builder<Id<BatchQueueItem>, Pair<I, BatchItemOutcome>> result = ImmutableSortedMap.naturalOrder();
    for (Map.Entry<Id<BatchQueueItem>, Pair<I, BatchItemOutcome>> entry : outcomes.left.rows.entrySet()) {
      Id<BatchQueueItem> itemId = entry.getKey();
      I item = entry.getValue().getLeft();
      BatchItemOutcome outcome = entry.getValue().getRight();
      if (outcome instanceof BatchItemOutcome.Retry) {
        BatchItemOutcome.Retry retry = (BatchItemOutcome.Retry) outcome;
        InflatedBatch<B, I> batch = itemsToBatch.get(entry.getKey());
        if (retry.getMaxTries().isDefined() && batch.getTryNumber() >= retry.getMaxTries().getOrThrow()) {
          String message = errorMessagesCache.computeIfAbsent(retry.getMaxTries().getOrThrow(), k -> {
            StringBuilder builder = new StringBuilder();
            builder.append("Exhausted tries (")
                .append(retry.getMaxTries().getOrThrow())
                .append(")");
            for (Exception thrown : outcomes.right) {
              builder.append(": ").append(thrown.getClass().getSimpleName());
              if (thrown.getMessage() != null) {
                builder.append(": ").append(thrown.getMessage());
              }
            }
            return builder.toString();
          });
          
          result.put(itemId, Pair.of(item, new BatchItemOutcome.ErrorFlag(message)));
        } else {
          result.put(entry.getKey(), entry.getValue());
        }
      } else {
        result.put(entry.getKey(), entry.getValue());
      }
    }
    if (!errorMessagesCache.isEmpty() && outcomes.right.isDefined()) {
      addToStm(queue, outcomes.right.getOrThrow());
    }
    return new RowsToOutcomes<>(result.build());
  }

  private boolean canProcess(BatchQueueBatch batch) {
    return batch.getPolledAt().isDefined() &&
        batch.getPolledBy().getOrThrow().equals(serviceDescriptor.getId()) &&
        batch.getSentAt().isEmpty() &&
        batch.getIgnoredAt().isEmpty() &&
        batch.getErroredAt().isEmpty();
  }
  
  private void safelyObserveSuccessfulBatch(BatchQueue<?, ?> queue, BatchSizer batchSizer, int targetBatchSize, int actualBatchSize, Duration processTime) {
    try {
      batchSizer.observeSuccessfulBatch(targetBatchSize, actualBatchSize, processTime);
    } catch (RuntimeException ex) {
      log.error(ex, "Exception in observeSuccessfulBatch");
      addToStm(queue, ex);
    }
  }
  
  private void safelyObserveSuccessfulBatch(BatchQueue<?, ?> queue, BatchQueueThrottler throttler, Json.Value batchData) {
    try {
      throttler.observeSuccessfulBatch(batchData);
    } catch (RuntimeException ex) {
      log.error(ex, "Exception in observeSuccessfulBatch");
      addToStm(queue, ex);
    }
  }
  
  private Option<Integer> safelyObserveFailedBatchAndGetNewSize(BatchQueue<?, ?> queue, BatchSizer batchSizer, int targetBatchSize, int actualBatchSize, Duration processTime, Exception exception) {
    try {
      int newSize = batchSizer.observeFailedBatchAndGetNewSize(targetBatchSize, actualBatchSize, processTime, exception);
      if (newSize < 1) {
        addToStm(queue, new IllegalArgumentException("Batch size must be at least 1, but was " + newSize));
        return Option.none();
      }
      if (newSize == actualBatchSize) {
        return Option.none();
      }
      return Option.some(newSize);
    } catch (RuntimeException ex) {
      log.error(ex, "Exception in observeFailedBatchAndGetNewSize");
      addToStm(queue, ex);
      return Option.none();
    }
  }
  
  private Duration safelyObserveFailedBatchAndGetRetryDelay(BatchQueue<?, ?> queue, BatchQueueThrottler throttler, Json.Value batchData, int tryNumber, Option<Exception> exception) {
    try {
      return throttler.observeFailedBatchAndGetRetryDelay(batchData, tryNumber, exception);
    } catch (RuntimeException ex) {
      log.error(ex, "Exception in observeFailedBatchAndGetRetryDelay");
      addToStm(queue, ex);
      return Duration.ZERO;
    }
  }
  
  private static ContiguousSet<Id<BatchQueueItem>> addToSet(ContiguousSet<Id<BatchQueueItem>> set, Id<BatchQueueItem> item) {
    Range<Id<BatchQueueItem>> range = set.range();
    checkArgument(BatchQueueItem.DISCRETE_DOMAIN.next(range.upperEndpoint()).equals(item), "Cannot add %s to set %s", item, set);
    return ContiguousSet.create(Range.closed(range.lowerEndpoint(), item), BatchQueueItem.DISCRETE_DOMAIN);
  }

  private void addToStm(BatchQueue<?, ?> queue, Throwable throwable) {
    Set<Author> authors = AuthorToPagerDuty.getAuthors(queue.getPagerDevice());
    if (authors.isEmpty()) {
      stm.add(throwable);
    } else {
      stm.add(throwable, authors);
    }
  }
  
}
