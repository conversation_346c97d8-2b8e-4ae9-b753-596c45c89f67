package com.wealthfront.platform.queryengine;

import org.joda.time.Duration;

import com.google.inject.Inject;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Thunk;
import com.kaching.platform.guice.MutableSingleton;
import com.kaching.platform.queryengine.Query;

@MutableSingleton
public class DefaultInMemoryOnlineQueryTimeoutHandler implements InMemoryOnlineQueryTimeoutHandler {

  @Inject TimeoutHandlerInjectionBundle bundle;

  private final Thunk<InMemoryOnlineQueryTimeoutHandler> handlerThunk =
      Thunk.thunk(() -> ComposableTimeoutHandlerCombinators.sequence(
              // page immediately if any 100 queries time out within 10 minutes
              ComposableTimeoutHandlerCombinators.frequencyBuilder(Duration.standardMinutes(10), 100)
                  .build()
                  .finalize(bundle)
                  .withPagingSchedule(PagingSchedule.IMMEDIATE),

              // if we make it this far, page office hours
              ComposableTimeoutHandlerCombinators.alwaysPage()
                  .withPagingSchedule(PagingSchedule.OFFICE_HOURS)
          )
          .toInMemoryOnlineQueryTimeoutHandler());

  @Override
  public Option<PagingSchedule> handleQueryTimeout(Class<? extends Query<?>> queryClazz) {
    return handlerThunk.get().handleQueryTimeout(queryClazz);
  }

}
