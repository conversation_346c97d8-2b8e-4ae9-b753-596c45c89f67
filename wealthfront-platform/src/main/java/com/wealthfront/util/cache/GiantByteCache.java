package com.wealthfront.util.cache;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.SortedMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableRangeMap;
import com.google.common.collect.ImmutableSortedMap;
import com.google.common.collect.Range;
import com.google.common.collect.RangeMap;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.kaching.entities.PowerOfTwo;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Strings;
import com.kaching.platform.tinykv.impl.WrappedBytesEntry;
import com.kaching.platform.util.WrappedBytes;
import com.kaching.util.Sleeper;
import com.kaching.util.time.NanoTimeProvider;

public class GiantByteCache implements LoadingByteCache {
  
  private static final int LARGE_CUTOFF = 1_000;
  private static final RangeMap<Integer, PowerOfTwo> NUM_STRIPES = ImmutableRangeMap.<Integer, PowerOfTwo>builder()
      .put(Range.lessThan(LARGE_CUTOFF), new PowerOfTwo(2))
      .put(Range.closedOpen(LARGE_CUTOFF, 10_000), new PowerOfTwo(4))
      .put(Range.closedOpen(10_000, 250_000), new PowerOfTwo(8))
      .put(Range.atLeast(250_000), new PowerOfTwo(16))
      .build();
  
  private final int maxNumEntries;
  private final LoadFunction loadFunction;
  private final AtomicReference<LoadingByteCache> delegate = new AtomicReference<>();
  private final AtomicReference<ByteCacheStats> stats = new AtomicReference<>();

  GiantByteCache(int maxNumEntries, LoadFunction loadFunction) {
    this.maxNumEntries = maxNumEntries;
    this.loadFunction = loadFunction;
  }
  
  @Inject NanoTimeProvider nanoClock;
  @Inject Sleeper sleeper;
  @Inject @Named("ThreadLocalRandom") Supplier<Random> randomSupplier;

  @Override
  public Map<WrappedBytes, WrappedBytes> getOrLoad(Collection<WrappedBytes> keys) {
    for (LoadingByteCache delegate : getDelegate()) {
      return delegate.getOrLoad(keys);
    }
    return loadFunction.load(keys);
  }

  @Override
  public WrappedBytes getOrLoad(WrappedBytes key) {
    for (LoadingByteCache delegate : getDelegate()) {
      return delegate.getOrLoad(key);
    }
    return loadFunction.load(List.of(key)).get(key);
  }

  @Override
  public Collection<WrappedBytesEntry> getAllSorted() {
    for (LoadingByteCache delegate : getDelegate()) {
      return delegate.getAllSorted();
    }
    return List.of();
  }

  @Override
  public int size() {
    for (LoadingByteCache delegate : getDelegate()) {
      return delegate.size();
    }
    return 0;
  }

  @Override
  public Option<WrappedBytes> getIfPresent(WrappedBytes key, boolean recordRead) {
    for (LoadingByteCache delegate : getDelegate()) {
      return delegate.getIfPresent(key, recordRead);
    }
    return Option.none();
  }

  @Override
  public Map<WrappedBytes, WrappedBytes> getIfPresent(Collection<WrappedBytes> keys, boolean recordRead) {
    for (LoadingByteCache delegate : getDelegate()) {
      return delegate.getIfPresent(keys, recordRead);
    }
    return ImmutableSortedMap.of();
  }

  @Override
  public void putAll(Map<WrappedBytes, WrappedBytes> newValues) {
    for (LoadingByteCache delegate : getDelegate()) {
      delegate.putAll(newValues);
    }
  }

  @Override
  public void invalidate(Collection<WrappedBytes> keys) {
    for (LoadingByteCache delegate : getDelegate()) {
      delegate.invalidate(keys);
    }
  }

  @Override
  public synchronized void preload(SortedMap<WrappedBytes, WrappedBytes> entries) {
    ByteCacheStats newStats = new ByteCacheStats();
    LoadFunction wrappedLoad = wrapLoadFunctionForStats(nanoClock, newStats, loadFunction);
    LoadingByteCache delegate;
    if (isLarge()) {
      PowerOfTwo numStripes = getNumStripes(maxNumEntries);
      int maxStripeSize = maxNumEntries / (int) numStripes.asLong();
      delegate = new LoadLockingByteCache(new StripedByteCache(numStripes, () -> new BufferedStaticByteCache(randomSupplier, newStats, maxStripeSize)), wrappedLoad, sleeper, newStats);
    } else {
      delegate = new SimpleLoadingByteCache(maxNumEntries, newStats, wrappedLoad);
    }
    delegate.preload(entries);
    this.delegate.set(delegate);
    this.stats.set(newStats);
  }
  
  public synchronized void disableAndInvalidateAll() {
    this.delegate.set(null);
  }
  
  public synchronized void reenable() {
    preload(ImmutableSortedMap.of());
  }
  
  public boolean isEnabled() {
    return getDelegate().isDefined();
  }

  @Override
  public void runMaintenance() {
    for (LoadingByteCache delegate : getDelegate()) {
      delegate.runMaintenance();
    }
  }

  @Override
  public long estimateMemoryUsage() {
    for (LoadingByteCache delegate : getDelegate()) {
      return delegate.estimateMemoryUsage();
    }
    return 0;
  }
  
  public int getMaxNumEntries() {
    return maxNumEntries;
  }

  public ByteCacheStats getStats() {
    for (LoadingByteCache delegate : getDelegate()) {
      if (delegate instanceof SimpleLoadingByteCache simple) {
        simple.refreshStats();
      }
    }
    ByteCacheStats currentStats = stats.getAcquire();
    if (currentStats == null) {
      return new ByteCacheStats();
    }
    currentStats.setNumEntries((long) size());
    currentStats.setMemoryUsageBytes(estimateMemoryUsage());
    currentStats.setMaxNumEntries((long) maxNumEntries);
    return currentStats;
  }

  static LoadFunction wrapLoadFunctionForStats(NanoTimeProvider nanoClock, ByteCacheStats stats, LoadFunction function) {
    return keys -> {
      long nanoStart = nanoClock.get();
      Map<WrappedBytes, WrappedBytes> result = function.load(keys);
      stats.addTotalLoadTimeNs(nanoClock.get() - nanoStart);
      int hits = 0;
      int misses = 0;
      for (WrappedBytes key : keys) {
        WrappedBytes value = result.get(key);
        if (value == null) {
          throw new IllegalArgumentException(Strings.format("Cache load function did not return a value for key %s", key));
        }
        if (LoadingByteCache.NO_VALUE.equals(value)) {
          misses++;
        } else {
          hits++;
        }
      }
      stats.addNumLoadHits(hits);
      stats.addNumLoadMisses(misses);
      return result;
    };
  }
  
  @VisibleForTesting
  boolean isLarge() {
    return maxNumEntries > LARGE_CUTOFF;
  }
  
  @VisibleForTesting
  PowerOfTwo getNumStripes(int maxNumEntries) {
    return NUM_STRIPES.get(maxNumEntries);
  }
  
  @VisibleForTesting
  Option<LoadingByteCache> getDelegate() {
    return Option.of(delegate.getAcquire());
  }
  
}
