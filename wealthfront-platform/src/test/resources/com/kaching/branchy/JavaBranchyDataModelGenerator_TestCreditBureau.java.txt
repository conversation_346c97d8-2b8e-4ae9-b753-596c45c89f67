package com.wealthfront.auto.types.global;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import java.util.function.Supplier;

@ExposeType(
    namespace = ExposeType.RewriteNamespace.DO_NOT_COPY,
    value = ExposeTo.BACKEND
)
public enum TestCreditBureau {
  EQUIFAX,

  EXPERIAN,

  TRANSUNION,

  OTHER;

  public <T> T visit(Visitor<T> visitor) {
    switch (this) {
      case EQUIFAX:
        return visitor.caseEquifax();
      case EXPERIAN:
        return visitor.caseExperian();
      case TRANSUNION:
        return visitor.caseTransunion();
      case OTHER:
        return visitor.caseOther();
      default:
        throw new IllegalStateException();
    }
  }

  @Deprecated
  public interface Visitor<T> {
    T caseEquifax();

    T caseExperian();

    T caseTransunion();

    T caseOther();
  }

  @Deprecated
  public static class DefaultVisitor<T> implements Visitor<T> {
    private final Supplier<T> defaultValue;

    public DefaultVisitor(Supplier<T> defaultValue) {
      this.defaultValue = defaultValue;
    }

    public DefaultVisitor(T defaultValue) {
      this.defaultValue = () -> defaultValue;
    }

    @Override
    public T caseEquifax() {
      return defaultValue.get();
    }

    @Override
    public T caseExperian() {
      return defaultValue.get();
    }

    @Override
    public T caseTransunion() {
      return defaultValue.get();
    }

    @Override
    public T caseOther() {
      return defaultValue.get();
    }
  }
}
