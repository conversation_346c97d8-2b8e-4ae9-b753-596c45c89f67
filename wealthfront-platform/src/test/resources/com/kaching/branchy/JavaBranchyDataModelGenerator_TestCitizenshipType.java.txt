package com.wealthfront.auto.types.global;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import java.util.function.Supplier;

@ExposeType(
    namespace = ExposeType.RewriteNamespace.DO_NOT_COPY,
    value = ExposeTo.BACKEND
)
public enum TestCitizenshipType {
  US_CITIZEN,

  PERMANENT_RESIDENT,

  NON_PERMANENT_RESIDENT;

  public <T> T visit(Visitor<T> visitor) {
    switch (this) {
      case US_CITIZEN:
        return visitor.caseUsCitizen();
      case PERMANENT_RESIDENT:
        return visitor.casePermanentResident();
      case NON_PERMANENT_RESIDENT:
        return visitor.caseNonPermanentResident();
      default:
        throw new IllegalStateException();
    }
  }

  @Deprecated
  public interface Visitor<T> {
    T caseUsCitizen();

    T casePermanentResident();

    T caseNonPermanentResident();
  }

  @Deprecated
  public static class DefaultVisitor<T> implements Visitor<T> {
    private final Supplier<T> defaultValue;

    public DefaultVisitor(Supplier<T> defaultValue) {
      this.defaultValue = defaultValue;
    }

    public DefaultVisitor(T defaultValue) {
      this.defaultValue = () -> defaultValue;
    }

    @Override
    public T caseUsCitizen() {
      return defaultValue.get();
    }

    @Override
    public T casePermanentResident() {
      return defaultValue.get();
    }

    @Override
    public T caseNonPermanentResident() {
      return defaultValue.get();
    }
  }
}
