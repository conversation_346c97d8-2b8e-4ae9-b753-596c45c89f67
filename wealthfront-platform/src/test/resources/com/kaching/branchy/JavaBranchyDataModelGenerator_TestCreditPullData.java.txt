package com.wealthfront.auto.types.global;

import com.google.common.annotations.VisibleForTesting;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import javax.annotation.Nullable;

@Entity(
    discriminator = "credit-pull-data"
)
public class TestCreditPullData extends TestMortgageData {
  @Value(
      optional = true,
      nullable = true
  )
  private TestEntityAction action;

  @Value(
      optional = true,
      nullable = true
  )
  private String id;

  @Value(
      optional = true,
      nullable = true
  )
  private List<TestBorrowerData> borrowers;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean pullEquifax;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean pullExperian;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean pullTransUnion;

  @Value(
      optional = true,
      nullable = true
  )
  private TestCreditPullType creditPullType;

  @Value(
      optional = true,
      nullable = true
  )
  private TestCreditPullRequestType creditPullRequestType;

  @Value(
      optional = true,
      nullable = true
  )
  private TestCreditPullActionType creditPullActionType;

  @Value(
      optional = true,
      nullable = true
  )
  private TestCreditPullSyncStage creditPullSyncStage;

  @Value(
      optional = true,
      nullable = true
  )
  private Boolean syncFailed;

  @Value(
      optional = true,
      nullable = true
  )
  private List<String> borrowerIds;

  private Set<String> fieldsToNullSet;

  public TestCreditPullData() {
    // JSON
  }

  public TestCreditPullData(TestEntityAction action, String id, List<TestBorrowerData> borrowers,
      Boolean pullEquifax, Boolean pullExperian, Boolean pullTransUnion,
      TestCreditPullType creditPullType, TestCreditPullRequestType creditPullRequestType,
      TestCreditPullActionType creditPullActionType, TestCreditPullSyncStage creditPullSyncStage,
      Boolean syncFailed, List<String> borrowerIds) {
    this.action = action;
    this.id = id;
    this.borrowers = borrowers;
    this.pullEquifax = pullEquifax;
    this.pullExperian = pullExperian;
    this.pullTransUnion = pullTransUnion;
    this.creditPullType = creditPullType;
    this.creditPullRequestType = creditPullRequestType;
    this.creditPullActionType = creditPullActionType;
    this.creditPullSyncStage = creditPullSyncStage;
    this.syncFailed = syncFailed;
    this.borrowerIds = borrowerIds;
  }

  @Override
  public <T> T visit(TestMortgageData.Visitor<T> visitor) {
    return visitor.caseTestCreditPullData(this);
  }

  public Option<TestEntityAction> getAction() {
    return Option.of(action);
  }

  public Option<String> getId() {
    return Option.of(id);
  }

  public Option<List<TestBorrowerData>> getBorrowers() {
    return Option.of(borrowers);
  }

  public Option<Boolean> getPullEquifax() {
    return Option.of(pullEquifax);
  }

  public Option<Boolean> getPullExperian() {
    return Option.of(pullExperian);
  }

  public Option<Boolean> getPullTransUnion() {
    return Option.of(pullTransUnion);
  }

  public Option<TestCreditPullType> getCreditPullType() {
    return Option.of(creditPullType);
  }

  public Option<TestCreditPullRequestType> getCreditPullRequestType() {
    return Option.of(creditPullRequestType);
  }

  public Option<TestCreditPullActionType> getCreditPullActionType() {
    return Option.of(creditPullActionType);
  }

  public Option<TestCreditPullSyncStage> getCreditPullSyncStage() {
    return Option.of(creditPullSyncStage);
  }

  public Option<Boolean> getSyncFailed() {
    return Option.of(syncFailed);
  }

  public Option<List<String>> getBorrowerIds() {
    return Option.of(borrowerIds);
  }

  @Override
  public String getInternalId() {
    return this.id;
  }

  @Override
  public void setInternalId(String id) {
    this.id = id;
  }

  @Override
  public Set<String> getFieldsToNullSet() {
    return this.fieldsToNullSet == null ? Collections.emptySet() : this.fieldsToNullSet;
  }

  @Override
  public void setFieldsToNullSet(Set<String> fieldsToNullSet) {
    this.fieldsToNullSet = fieldsToNullSet;
  }

  @Override
  public void validate() {
    super.validate();
  }

  @Override
  public int hashCode() {
    return Objects.hash(super.hashCode(), this.action, this.id, this.borrowers, this.pullEquifax, this.pullExperian, this.pullTransUnion, this.creditPullType, this.creditPullRequestType, this.creditPullActionType, this.creditPullSyncStage, this.syncFailed, this.borrowerIds);
  }

  @Override
  public boolean equals(Object o) {
    if (o == this) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TestCreditPullData that = (TestCreditPullData) o;
    return super.equals(that) &&
        Objects.equals(action, that.action) &&
        Objects.equals(id, that.id) &&
        Objects.equals(borrowers, that.borrowers) &&
        Objects.equals(pullEquifax, that.pullEquifax) &&
        Objects.equals(pullExperian, that.pullExperian) &&
        Objects.equals(pullTransUnion, that.pullTransUnion) &&
        Objects.equals(creditPullType, that.creditPullType) &&
        Objects.equals(creditPullRequestType, that.creditPullRequestType) &&
        Objects.equals(creditPullActionType, that.creditPullActionType) &&
        Objects.equals(creditPullSyncStage, that.creditPullSyncStage) &&
        Objects.equals(syncFailed, that.syncFailed) &&
        Objects.equals(borrowerIds, that.borrowerIds);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    boolean isExactClass = this.getClass().equals(TestCreditPullData.class);
    if (isExactClass) {
      sb.append("TestCreditPullData {\n");
    }
    sb.append(super.toString());
    sb.append("  action: ").append(action).append("\n");
    sb.append("  id: ").append(id).append("\n");
    sb.append("  borrowers: ").append(borrowers == null ? "null" : borrowers.toString().replaceAll("\n", "\n  ")).append("\n");
    sb.append("  pullEquifax: ").append(pullEquifax).append("\n");
    sb.append("  pullExperian: ").append(pullExperian).append("\n");
    sb.append("  pullTransUnion: ").append(pullTransUnion).append("\n");
    sb.append("  creditPullType: ").append(creditPullType).append("\n");
    sb.append("  creditPullRequestType: ").append(creditPullRequestType).append("\n");
    sb.append("  creditPullActionType: ").append(creditPullActionType).append("\n");
    sb.append("  creditPullSyncStage: ").append(creditPullSyncStage).append("\n");
    sb.append("  syncFailed: ").append(syncFailed).append("\n");
    sb.append("  borrowerIds: ").append(borrowerIds == null ? "null" : borrowerIds.toString().replaceAll("\n", "\n  ")).append("\n");
    if (isExactClass) {
      sb.append("}");
    }
    return sb.toString();
  }

  public Builder copy() {
    return builder()
      .withAction(getAction().getOrNull())
      .withId(getId().getOrNull())
      .withBorrowers(getBorrowers().getOrNull())
      .withPullEquifax(getPullEquifax().getOrNull())
      .withPullExperian(getPullExperian().getOrNull())
      .withPullTransUnion(getPullTransUnion().getOrNull())
      .withCreditPullType(getCreditPullType().getOrNull())
      .withCreditPullRequestType(getCreditPullRequestType().getOrNull())
      .withCreditPullActionType(getCreditPullActionType().getOrNull())
      .withCreditPullSyncStage(getCreditPullSyncStage().getOrNull())
      .withSyncFailed(getSyncFailed().getOrNull())
      .withBorrowerIds(getBorrowerIds().getOrNull());
  }

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder {
    @Nullable
    private TestEntityAction action = null;

    @Nullable
    private String id = null;

    @Nullable
    private List<TestBorrowerData> borrowers = new ArrayList<>();

    @Nullable
    private Boolean pullEquifax = null;

    @Nullable
    private Boolean pullExperian = null;

    @Nullable
    private Boolean pullTransUnion = null;

    @Nullable
    private TestCreditPullType creditPullType = null;

    @Nullable
    private TestCreditPullRequestType creditPullRequestType = null;

    @Nullable
    private TestCreditPullActionType creditPullActionType = null;

    @Nullable
    private TestCreditPullSyncStage creditPullSyncStage = null;

    @Nullable
    private Boolean syncFailed = null;

    @Nullable
    private List<String> borrowerIds = new ArrayList<>();

    public Builder withAction(@Nullable TestEntityAction action) {
      this.action = action;
      return this;
    }

    public Builder withId(@Nullable String id) {
      this.id = id;
      return this;
    }

    public Builder withBorrowers(@Nullable List<TestBorrowerData> borrowers) {
      this.borrowers = borrowers;
      return this;
    }

    public Builder withPullEquifax(@Nullable Boolean pullEquifax) {
      this.pullEquifax = pullEquifax;
      return this;
    }

    public Builder withPullExperian(@Nullable Boolean pullExperian) {
      this.pullExperian = pullExperian;
      return this;
    }

    public Builder withPullTransUnion(@Nullable Boolean pullTransUnion) {
      this.pullTransUnion = pullTransUnion;
      return this;
    }

    public Builder withCreditPullType(@Nullable TestCreditPullType creditPullType) {
      this.creditPullType = creditPullType;
      return this;
    }

    public Builder withCreditPullRequestType(
        @Nullable TestCreditPullRequestType creditPullRequestType) {
      this.creditPullRequestType = creditPullRequestType;
      return this;
    }

    public Builder withCreditPullActionType(
        @Nullable TestCreditPullActionType creditPullActionType) {
      this.creditPullActionType = creditPullActionType;
      return this;
    }

    public Builder withCreditPullSyncStage(@Nullable TestCreditPullSyncStage creditPullSyncStage) {
      this.creditPullSyncStage = creditPullSyncStage;
      return this;
    }

    public Builder withSyncFailed(@Nullable Boolean syncFailed) {
      this.syncFailed = syncFailed;
      return this;
    }

    public Builder withBorrowerIds(@Nullable List<String> borrowerIds) {
      this.borrowerIds = borrowerIds;
      return this;
    }

    public TestCreditPullData build() {
      TestCreditPullData obj1 = new TestCreditPullData(action, id, borrowers, pullEquifax, pullExperian, pullTransUnion, creditPullType, creditPullRequestType, creditPullActionType, creditPullSyncStage, syncFailed, borrowerIds);
      obj1.validate();
      return obj1;
    }

    @VisibleForTesting
    public TestCreditPullData buildForTesting() {
      TestCreditPullData obj1 = new TestCreditPullData(action, id, borrowers, pullEquifax, pullExperian, pullTransUnion, creditPullType, creditPullRequestType, creditPullActionType, creditPullSyncStage, syncFailed, borrowerIds);
      return obj1;
    }
  }
}
