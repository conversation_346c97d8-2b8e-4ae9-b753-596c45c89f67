package com.wealthfront.auto.types.global;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import java.util.function.Supplier;

@ExposeType(
    namespace = ExposeType.RewriteNamespace.DO_NOT_COPY,
    value = ExposeTo.BACKEND
)
public enum TestMilitaryStatus {
  NONE,

  ACTIVE_DUTY,

  RESERVE_NEVER_ACTIVATED,

  SURVIVING_SPOUSE,

  VETERAN;

  public <T> T visit(Visitor<T> visitor) {
    switch (this) {
      case NONE:
        return visitor.caseNone();
      case ACTIVE_DUTY:
        return visitor.caseActiveDuty();
      case RESERVE_NEVER_ACTIVATED:
        return visitor.caseReserveNeverActivated();
      case SURVIVING_SPOUSE:
        return visitor.caseSurvivingSpouse();
      case VETERAN:
        return visitor.caseVeteran();
      default:
        throw new IllegalStateException();
    }
  }

  @Deprecated
  public interface Visitor<T> {
    T caseNone();

    T caseActiveDuty();

    T caseReserveNeverActivated();

    T caseSurvivingSpouse();

    T caseVeteran();
  }

  @Deprecated
  public static class DefaultVisitor<T> implements Visitor<T> {
    private final Supplier<T> defaultValue;

    public DefaultVisitor(Supplier<T> defaultValue) {
      this.defaultValue = defaultValue;
    }

    public DefaultVisitor(T defaultValue) {
      this.defaultValue = () -> defaultValue;
    }

    @Override
    public T caseNone() {
      return defaultValue.get();
    }

    @Override
    public T caseActiveDuty() {
      return defaultValue.get();
    }

    @Override
    public T caseReserveNeverActivated() {
      return defaultValue.get();
    }

    @Override
    public T caseSurvivingSpouse() {
      return defaultValue.get();
    }

    @Override
    public T caseVeteran() {
      return defaultValue.get();
    }
  }
}
