package com.wealthfront.auto.types.global;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import java.util.function.Supplier;

@ExposeType(
    namespace = ExposeType.RewriteNamespace.DO_NOT_COPY,
    value = ExposeTo.BACKEND
)
public enum TestCreditPullStatus {
  NOT_STARTED,

  IN_PROGRESS,

  FAILED,

  SUCCEEDED;

  public <T> T visit(Visitor<T> visitor) {
    switch (this) {
      case NOT_STARTED:
        return visitor.caseNotStarted();
      case IN_PROGRESS:
        return visitor.caseInProgress();
      case FAILED:
        return visitor.caseFailed();
      case SUCCEEDED:
        return visitor.caseSucceeded();
      default:
        throw new IllegalStateException();
    }
  }

  @Deprecated
  public interface Visitor<T> {
    T caseNotStarted();

    T caseInProgress();

    T caseFailed();

    T caseSucceeded();
  }

  @Deprecated
  public static class DefaultVisitor<T> implements Visitor<T> {
    private final Supplier<T> defaultValue;

    public DefaultVisitor(Supplier<T> defaultValue) {
      this.defaultValue = defaultValue;
    }

    public DefaultVisitor(T defaultValue) {
      this.defaultValue = () -> defaultValue;
    }

    @Override
    public T caseNotStarted() {
      return defaultValue.get();
    }

    @Override
    public T caseInProgress() {
      return defaultValue.get();
    }

    @Override
    public T caseFailed() {
      return defaultValue.get();
    }

    @Override
    public T caseSucceeded() {
      return defaultValue.get();
    }
  }
}
