package com.wealthfront.auto.types.global;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import java.util.function.Supplier;

@ExposeType(
    namespace = ExposeType.RewriteNamespace.DO_NOT_COPY,
    value = ExposeTo.BACKEND
)
public enum TestEntityAction {
  ADD,

  REMOVE,

  UPDATE;

  public <T> T visit(Visitor<T> visitor) {
    switch (this) {
      case ADD:
        return visitor.caseAdd();
      case REMOVE:
        return visitor.caseRemove();
      case UPDATE:
        return visitor.caseUpdate();
      default:
        throw new IllegalStateException();
    }
  }

  @Deprecated
  public interface Visitor<T> {
    T caseAdd();

    T caseRemove();

    T caseUpdate();
  }

  @Deprecated
  public static class DefaultVisitor<T> implements Visitor<T> {
    private final Supplier<T> defaultValue;

    public DefaultVisitor(Supplier<T> defaultValue) {
      this.defaultValue = defaultValue;
    }

    public DefaultVisitor(T defaultValue) {
      this.defaultValue = () -> defaultValue;
    }

    @Override
    public T caseAdd() {
      return defaultValue.get();
    }

    @Override
    public T caseRemove() {
      return defaultValue.get();
    }

    @Override
    public T caseUpdate() {
      return defaultValue.get();
    }
  }
}
