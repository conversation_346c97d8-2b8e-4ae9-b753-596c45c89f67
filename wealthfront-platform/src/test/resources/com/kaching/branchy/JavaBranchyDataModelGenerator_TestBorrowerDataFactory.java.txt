package com.wealthfront.lending.mortgages.application.model.data.factory;

import com.google.inject.Provider;
import com.kaching.entities.EmailAddress;
import com.kaching.entities.PhoneNumber;
import com.kaching.platform.common.Option;
import com.wealthfront.auto.types.global.TestAddressData;
import com.wealthfront.auto.types.global.TestBankruptcyData;
import com.wealthfront.auto.types.global.TestBorrowerData;
import com.wealthfront.auto.types.global.TestBorrowerType;
import com.wealthfront.auto.types.global.TestBorrowerWorkflowData;
import com.wealthfront.auto.types.global.TestCitizenshipType;
import com.wealthfront.auto.types.global.TestCreditPullConsentType;
import com.wealthfront.auto.types.global.TestCreditPullStatus;
import com.wealthfront.auto.types.global.TestCreditPullType;
import com.wealthfront.auto.types.global.TestCreditScoreData;
import com.wealthfront.auto.types.global.TestEmploymentStatus;
import com.wealthfront.auto.types.global.TestEntityAction;
import com.wealthfront.auto.types.global.TestIncomeWorkflowData;
import com.wealthfront.auto.types.global.TestMaritalStatus;
import com.wealthfront.auto.types.global.TestMilitaryStatus;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;

public class TestBorrowerDataFactory {
  public static TestBorrowerDataBuilder createBorrowerData(Provider<UUID> uuidProvider) {
    TestBorrowerDataBuilder builder = new TestBorrowerDataBuilder();
    builder.withId(uuidProvider.get().toString());
    builder.withAction(TestEntityAction.ADD);
    return builder;
  }

  public static TestBorrowerDataBuilder updateBorrowerData(String id) {
    TestBorrowerDataBuilder builder = new TestBorrowerDataBuilder();
    builder.withId(id);
    builder.withAction(TestEntityAction.UPDATE);
    return builder;
  }

  public static TestBorrowerData deleteBorrowerData(String id) {
    TestBorrowerDataBuilder builder = new TestBorrowerDataBuilder();
    builder.withId(id);
    builder.withAction(TestEntityAction.REMOVE);
    return builder.build();
  }

  public static class TestBorrowerDataBuilder {
    private TestEntityAction action;

    private String id;

    private Option<TestIncomeWorkflowData> incomeWorkflowData;

    private Option<TestBorrowerType> borrowerType;

    private Option<String> firstName;

    private Option<String> middleName;

    private Option<String> lastName;

    private Option<String> suffix;

    private Option<List<String>> alternativeNames;

    private Option<DateTime> termsOfServiceConsentedAt;

    private Option<DateTime> creditPullConsentedAt;

    private Option<EmailAddress> emailAddress;

    private Option<PhoneNumber> phoneNumber;

    private Option<LocalDate> dateOfBirth;

    private Option<TestAddressData> currentAddress;

    private Option<TestAddressData> mailingAddress;

    private Option<TestMaritalStatus> maritalStatus;

    private Option<TestCitizenshipType> citizenshipType;

    private Option<String> spouseBorrowerId;

    private Option<Boolean> isCurrentlyLivingWithCoBorrowers;

    private Option<Boolean> isIntendingToOccupy;

    private Option<Boolean> isPrimaryAuthorized;

    private Option<Boolean> isSharingInformationWithCoBorrowers;

    private Option<TestMilitaryStatus> militaryStatus;

    private Option<LocalDate> militaryServiceExpectedCompletionDate;

    private Option<TestEmploymentStatus> employmentStatus;

    private Option<TestCreditPullConsentType> softCreditPullConsentType;

    private Option<TestCreditPullStatus> creditPullStatus;

    private Option<TestCreditPullType> creditPullType;

    private Option<List<TestCreditScoreData>> creditScoreData;

    private Option<TestBorrowerWorkflowData> borrowerWorkflowData;

    private Option<Boolean> homeownerPastThreeYears;

    private Option<Boolean> outstandingJudgmentsIndicator;

    private Option<Boolean> presentlyDelinquentIndicator;

    private Option<Boolean> partyToLawsuitIndicator;

    private Option<Boolean> priorPropertyDeedInLieuConveyedIndicator;

    private Option<Boolean> priorPropertyShortSaleCompletedIndicator;

    private Option<Boolean> priorPropertyForeclosureCompletedIndicator;

    private Option<Boolean> bankruptcyIndicator;

    private Option<List<TestBankruptcyData>> bankruptcies;

    public TestBorrowerDataBuilder withIncomeWorkflowData(
        TestIncomeWorkflowData incomeWorkflowData) {
      this.incomeWorkflowData = Option.of(incomeWorkflowData);
      return this;
    }

    public TestBorrowerDataBuilder withBorrowerType(TestBorrowerType borrowerType) {
      this.borrowerType = Option.of(borrowerType);
      return this;
    }

    public TestBorrowerDataBuilder withFirstName(String firstName) {
      this.firstName = Option.of(firstName);
      return this;
    }

    public TestBorrowerDataBuilder withMiddleName(String middleName) {
      this.middleName = Option.of(middleName);
      return this;
    }

    public TestBorrowerDataBuilder withLastName(String lastName) {
      this.lastName = Option.of(lastName);
      return this;
    }

    public TestBorrowerDataBuilder withSuffix(String suffix) {
      this.suffix = Option.of(suffix);
      return this;
    }

    public TestBorrowerDataBuilder withAlternativeNames(List<String> alternativeNames) {
      this.alternativeNames = Option.of(alternativeNames);
      return this;
    }

    public TestBorrowerDataBuilder withTermsOfServiceConsentedAt(
        DateTime termsOfServiceConsentedAt) {
      this.termsOfServiceConsentedAt = Option.of(termsOfServiceConsentedAt);
      return this;
    }

    public TestBorrowerDataBuilder withCreditPullConsentedAt(DateTime creditPullConsentedAt) {
      this.creditPullConsentedAt = Option.of(creditPullConsentedAt);
      return this;
    }

    public TestBorrowerDataBuilder withEmailAddress(EmailAddress emailAddress) {
      this.emailAddress = Option.of(emailAddress);
      return this;
    }

    public TestBorrowerDataBuilder withPhoneNumber(PhoneNumber phoneNumber) {
      this.phoneNumber = Option.of(phoneNumber);
      return this;
    }

    public TestBorrowerDataBuilder withDateOfBirth(LocalDate dateOfBirth) {
      this.dateOfBirth = Option.of(dateOfBirth);
      return this;
    }

    public TestBorrowerDataBuilder withCurrentAddress(TestAddressData currentAddress) {
      this.currentAddress = Option.of(currentAddress);
      return this;
    }

    public TestBorrowerDataBuilder withMailingAddress(TestAddressData mailingAddress) {
      this.mailingAddress = Option.of(mailingAddress);
      return this;
    }

    public TestBorrowerDataBuilder withMaritalStatus(TestMaritalStatus maritalStatus) {
      this.maritalStatus = Option.of(maritalStatus);
      return this;
    }

    public TestBorrowerDataBuilder withCitizenshipType(TestCitizenshipType citizenshipType) {
      this.citizenshipType = Option.of(citizenshipType);
      return this;
    }

    public TestBorrowerDataBuilder withSpouseBorrowerId(String spouseBorrowerId) {
      this.spouseBorrowerId = Option.of(spouseBorrowerId);
      return this;
    }

    public TestBorrowerDataBuilder withIsCurrentlyLivingWithCoBorrowers(
        Boolean isCurrentlyLivingWithCoBorrowers) {
      this.isCurrentlyLivingWithCoBorrowers = Option.of(isCurrentlyLivingWithCoBorrowers);
      return this;
    }

    public TestBorrowerDataBuilder withIsIntendingToOccupy(Boolean isIntendingToOccupy) {
      this.isIntendingToOccupy = Option.of(isIntendingToOccupy);
      return this;
    }

    public TestBorrowerDataBuilder withIsPrimaryAuthorized(Boolean isPrimaryAuthorized) {
      this.isPrimaryAuthorized = Option.of(isPrimaryAuthorized);
      return this;
    }

    public TestBorrowerDataBuilder withIsSharingInformationWithCoBorrowers(
        Boolean isSharingInformationWithCoBorrowers) {
      this.isSharingInformationWithCoBorrowers = Option.of(isSharingInformationWithCoBorrowers);
      return this;
    }

    public TestBorrowerDataBuilder withMilitaryStatus(TestMilitaryStatus militaryStatus) {
      this.militaryStatus = Option.of(militaryStatus);
      return this;
    }

    public TestBorrowerDataBuilder withMilitaryServiceExpectedCompletionDate(
        LocalDate militaryServiceExpectedCompletionDate) {
      this.militaryServiceExpectedCompletionDate = Option.of(militaryServiceExpectedCompletionDate);
      return this;
    }

    public TestBorrowerDataBuilder withEmploymentStatus(TestEmploymentStatus employmentStatus) {
      this.employmentStatus = Option.of(employmentStatus);
      return this;
    }

    public TestBorrowerDataBuilder withSoftCreditPullConsentType(
        TestCreditPullConsentType softCreditPullConsentType) {
      this.softCreditPullConsentType = Option.of(softCreditPullConsentType);
      return this;
    }

    public TestBorrowerDataBuilder withCreditPullStatus(TestCreditPullStatus creditPullStatus) {
      this.creditPullStatus = Option.of(creditPullStatus);
      return this;
    }

    public TestBorrowerDataBuilder withCreditPullType(TestCreditPullType creditPullType) {
      this.creditPullType = Option.of(creditPullType);
      return this;
    }

    public TestBorrowerDataBuilder withCreditScoreData(List<TestCreditScoreData> creditScoreData) {
      this.creditScoreData = Option.of(creditScoreData);
      return this;
    }

    public TestBorrowerDataBuilder withBorrowerWorkflowData(
        TestBorrowerWorkflowData borrowerWorkflowData) {
      this.borrowerWorkflowData = Option.of(borrowerWorkflowData);
      return this;
    }

    public TestBorrowerDataBuilder withHomeownerPastThreeYears(Boolean homeownerPastThreeYears) {
      this.homeownerPastThreeYears = Option.of(homeownerPastThreeYears);
      return this;
    }

    public TestBorrowerDataBuilder withOutstandingJudgmentsIndicator(
        Boolean outstandingJudgmentsIndicator) {
      this.outstandingJudgmentsIndicator = Option.of(outstandingJudgmentsIndicator);
      return this;
    }

    public TestBorrowerDataBuilder withPresentlyDelinquentIndicator(
        Boolean presentlyDelinquentIndicator) {
      this.presentlyDelinquentIndicator = Option.of(presentlyDelinquentIndicator);
      return this;
    }

    public TestBorrowerDataBuilder withPartyToLawsuitIndicator(Boolean partyToLawsuitIndicator) {
      this.partyToLawsuitIndicator = Option.of(partyToLawsuitIndicator);
      return this;
    }

    public TestBorrowerDataBuilder withPriorPropertyDeedInLieuConveyedIndicator(
        Boolean priorPropertyDeedInLieuConveyedIndicator) {
      this.priorPropertyDeedInLieuConveyedIndicator = Option.of(priorPropertyDeedInLieuConveyedIndicator);
      return this;
    }

    public TestBorrowerDataBuilder withPriorPropertyShortSaleCompletedIndicator(
        Boolean priorPropertyShortSaleCompletedIndicator) {
      this.priorPropertyShortSaleCompletedIndicator = Option.of(priorPropertyShortSaleCompletedIndicator);
      return this;
    }

    public TestBorrowerDataBuilder withPriorPropertyForeclosureCompletedIndicator(
        Boolean priorPropertyForeclosureCompletedIndicator) {
      this.priorPropertyForeclosureCompletedIndicator = Option.of(priorPropertyForeclosureCompletedIndicator);
      return this;
    }

    public TestBorrowerDataBuilder withBankruptcyIndicator(Boolean bankruptcyIndicator) {
      this.bankruptcyIndicator = Option.of(bankruptcyIndicator);
      return this;
    }

    public TestBorrowerDataBuilder withBankruptcies(List<TestBankruptcyData> bankruptcies) {
      this.bankruptcies = Option.of(bankruptcies);
      return this;
    }

    private TestBorrowerDataBuilder withId(String id) {
      this.id = id;
      return this;
    }

    public TestBorrowerData build() {
      TestBorrowerData entity = new TestBorrowerData(
        id == null ? null : id.getOrNull(),
        incomeWorkflowData == null ? null : incomeWorkflowData.getOrNull(),
        borrowerType == null ? null : borrowerType.getOrNull(),
        firstName == null ? null : firstName.getOrNull(),
        middleName == null ? null : middleName.getOrNull(),
        lastName == null ? null : lastName.getOrNull(),
        suffix == null ? null : suffix.getOrNull(),
        alternativeNames == null ? null : alternativeNames.getOrNull(),
        termsOfServiceConsentedAt == null ? null : termsOfServiceConsentedAt.getOrNull(),
        creditPullConsentedAt == null ? null : creditPullConsentedAt.getOrNull(),
        emailAddress == null ? null : emailAddress.getOrNull(),
        phoneNumber == null ? null : phoneNumber.getOrNull(),
        dateOfBirth == null ? null : dateOfBirth.getOrNull(),
        currentAddress == null ? null : currentAddress.getOrNull(),
        mailingAddress == null ? null : mailingAddress.getOrNull(),
        maritalStatus == null ? null : maritalStatus.getOrNull(),
        citizenshipType == null ? null : citizenshipType.getOrNull(),
        spouseBorrowerId == null ? null : spouseBorrowerId.getOrNull(),
        isCurrentlyLivingWithCoBorrowers == null ? null : isCurrentlyLivingWithCoBorrowers.getOrNull(),
        isIntendingToOccupy == null ? null : isIntendingToOccupy.getOrNull(),
        isPrimaryAuthorized == null ? null : isPrimaryAuthorized.getOrNull(),
        isSharingInformationWithCoBorrowers == null ? null : isSharingInformationWithCoBorrowers.getOrNull(),
        militaryStatus == null ? null : militaryStatus.getOrNull(),
        militaryServiceExpectedCompletionDate == null ? null : militaryServiceExpectedCompletionDate.getOrNull(),
        employmentStatus == null ? null : employmentStatus.getOrNull(),
        softCreditPullConsentType == null ? null : softCreditPullConsentType.getOrNull(),
        creditPullStatus == null ? null : creditPullStatus.getOrNull(),
        creditPullType == null ? null : creditPullType.getOrNull(),
        creditScoreData == null ? null : creditScoreData.getOrNull(),
        borrowerWorkflowData == null ? null : borrowerWorkflowData.getOrNull(),
        homeownerPastThreeYears == null ? null : homeownerPastThreeYears.getOrNull(),
        outstandingJudgmentsIndicator == null ? null : outstandingJudgmentsIndicator.getOrNull(),
        presentlyDelinquentIndicator == null ? null : presentlyDelinquentIndicator.getOrNull(),
        partyToLawsuitIndicator == null ? null : partyToLawsuitIndicator.getOrNull(),
        priorPropertyDeedInLieuConveyedIndicator == null ? null : priorPropertyDeedInLieuConveyedIndicator.getOrNull(),
        priorPropertyShortSaleCompletedIndicator == null ? null : priorPropertyShortSaleCompletedIndicator.getOrNull(),
        priorPropertyForeclosureCompletedIndicator == null ? null : priorPropertyForeclosureCompletedIndicator.getOrNull(),
        bankruptcyIndicator == null ? null : bankruptcyIndicator.getOrNull(),
        bankruptcies == null ? null : bankruptcies.getOrNull()
      );
      entity.setId(this.id);
      entity.setFieldsToNullSet(getFieldsToNullSet());
      return entity;
    }

    private Set<String> getFieldsToNullSet() {
      Set<String> fieldsToNullSet = new HashSet<>();
      if (incomeWorkflowData != null && incomeWorkflowData.isEmpty()) {
        fieldsToNullSet.add("incomeWorkflowData");
      }
      if (borrowerType != null && borrowerType.isEmpty()) {
        fieldsToNullSet.add("borrowerType");
      }
      if (firstName != null && firstName.isEmpty()) {
        fieldsToNullSet.add("firstName");
      }
      if (middleName != null && middleName.isEmpty()) {
        fieldsToNullSet.add("middleName");
      }
      if (lastName != null && lastName.isEmpty()) {
        fieldsToNullSet.add("lastName");
      }
      if (suffix != null && suffix.isEmpty()) {
        fieldsToNullSet.add("suffix");
      }
      if (alternativeNames != null && alternativeNames.isEmpty()) {
        fieldsToNullSet.add("alternativeNames");
      }
      if (termsOfServiceConsentedAt != null && termsOfServiceConsentedAt.isEmpty()) {
        fieldsToNullSet.add("termsOfServiceConsentedAt");
      }
      if (creditPullConsentedAt != null && creditPullConsentedAt.isEmpty()) {
        fieldsToNullSet.add("creditPullConsentedAt");
      }
      if (emailAddress != null && emailAddress.isEmpty()) {
        fieldsToNullSet.add("emailAddress");
      }
      if (phoneNumber != null && phoneNumber.isEmpty()) {
        fieldsToNullSet.add("phoneNumber");
      }
      if (dateOfBirth != null && dateOfBirth.isEmpty()) {
        fieldsToNullSet.add("dateOfBirth");
      }
      if (currentAddress != null && currentAddress.isEmpty()) {
        fieldsToNullSet.add("currentAddress");
      }
      if (mailingAddress != null && mailingAddress.isEmpty()) {
        fieldsToNullSet.add("mailingAddress");
      }
      if (maritalStatus != null && maritalStatus.isEmpty()) {
        fieldsToNullSet.add("maritalStatus");
      }
      if (citizenshipType != null && citizenshipType.isEmpty()) {
        fieldsToNullSet.add("citizenshipType");
      }
      if (spouseBorrowerId != null && spouseBorrowerId.isEmpty()) {
        fieldsToNullSet.add("spouseBorrowerId");
      }
      if (isCurrentlyLivingWithCoBorrowers != null && isCurrentlyLivingWithCoBorrowers.isEmpty()) {
        fieldsToNullSet.add("isCurrentlyLivingWithCoBorrowers");
      }
      if (isIntendingToOccupy != null && isIntendingToOccupy.isEmpty()) {
        fieldsToNullSet.add("isIntendingToOccupy");
      }
      if (isPrimaryAuthorized != null && isPrimaryAuthorized.isEmpty()) {
        fieldsToNullSet.add("isPrimaryAuthorized");
      }
      if (isSharingInformationWithCoBorrowers != null && isSharingInformationWithCoBorrowers.isEmpty()) {
        fieldsToNullSet.add("isSharingInformationWithCoBorrowers");
      }
      if (militaryStatus != null && militaryStatus.isEmpty()) {
        fieldsToNullSet.add("militaryStatus");
      }
      if (militaryServiceExpectedCompletionDate != null && militaryServiceExpectedCompletionDate.isEmpty()) {
        fieldsToNullSet.add("militaryServiceExpectedCompletionDate");
      }
      if (employmentStatus != null && employmentStatus.isEmpty()) {
        fieldsToNullSet.add("employmentStatus");
      }
      if (softCreditPullConsentType != null && softCreditPullConsentType.isEmpty()) {
        fieldsToNullSet.add("softCreditPullConsentType");
      }
      if (creditPullStatus != null && creditPullStatus.isEmpty()) {
        fieldsToNullSet.add("creditPullStatus");
      }
      if (creditPullType != null && creditPullType.isEmpty()) {
        fieldsToNullSet.add("creditPullType");
      }
      if (creditScoreData != null && creditScoreData.isEmpty()) {
        fieldsToNullSet.add("creditScoreData");
      }
      if (borrowerWorkflowData != null && borrowerWorkflowData.isEmpty()) {
        fieldsToNullSet.add("borrowerWorkflowData");
      }
      if (homeownerPastThreeYears != null && homeownerPastThreeYears.isEmpty()) {
        fieldsToNullSet.add("homeownerPastThreeYears");
      }
      if (outstandingJudgmentsIndicator != null && outstandingJudgmentsIndicator.isEmpty()) {
        fieldsToNullSet.add("outstandingJudgmentsIndicator");
      }
      if (presentlyDelinquentIndicator != null && presentlyDelinquentIndicator.isEmpty()) {
        fieldsToNullSet.add("presentlyDelinquentIndicator");
      }
      if (partyToLawsuitIndicator != null && partyToLawsuitIndicator.isEmpty()) {
        fieldsToNullSet.add("partyToLawsuitIndicator");
      }
      if (priorPropertyDeedInLieuConveyedIndicator != null && priorPropertyDeedInLieuConveyedIndicator.isEmpty()) {
        fieldsToNullSet.add("priorPropertyDeedInLieuConveyedIndicator");
      }
      if (priorPropertyShortSaleCompletedIndicator != null && priorPropertyShortSaleCompletedIndicator.isEmpty()) {
        fieldsToNullSet.add("priorPropertyShortSaleCompletedIndicator");
      }
      if (priorPropertyForeclosureCompletedIndicator != null && priorPropertyForeclosureCompletedIndicator.isEmpty()) {
        fieldsToNullSet.add("priorPropertyForeclosureCompletedIndicator");
      }
      if (bankruptcyIndicator != null && bankruptcyIndicator.isEmpty()) {
        fieldsToNullSet.add("bankruptcyIndicator");
      }
      if (bankruptcies != null && bankruptcies.isEmpty()) {
        fieldsToNullSet.add("bankruptcies");
      }
      return fieldsToNullSet;
    }
  }
}
