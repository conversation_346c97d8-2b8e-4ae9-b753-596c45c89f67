package com.wealthfront.auto.types.global;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import java.util.function.Supplier;

@ExposeType(
    namespace = ExposeType.RewriteNamespace.DO_NOT_COPY,
    value = ExposeTo.BACKEND
)
public enum TestCreditPullConsentType {
  VERBAL,

  WRITTEN,

  ELECTRONIC;

  public <T> T visit(Visitor<T> visitor) {
    switch (this) {
      case VERBAL:
        return visitor.caseVerbal();
      case WRITTEN:
        return visitor.caseWritten();
      case ELECTRONIC:
        return visitor.caseElectronic();
      default:
        throw new IllegalStateException();
    }
  }

  @Deprecated
  public interface Visitor<T> {
    T caseVerbal();

    T caseWritten();

    T caseElectronic();
  }

  @Deprecated
  public static class DefaultVisitor<T> implements Visitor<T> {
    private final Supplier<T> defaultValue;

    public DefaultVisitor(Supplier<T> defaultValue) {
      this.defaultValue = defaultValue;
    }

    public DefaultVisitor(T defaultValue) {
      this.defaultValue = () -> defaultValue;
    }

    @Override
    public T caseVerbal() {
      return defaultValue.get();
    }

    @Override
    public T caseWritten() {
      return defaultValue.get();
    }

    @Override
    public T caseElectronic() {
      return defaultValue.get();
    }
  }
}
