package com.demo;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Preconditions;
import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.entities.Money;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import java.util.Objects;
import javax.annotation.Nonnull;

@ExposeType(ExposeTo.LOCAL)
@Entity
public class SimpleEntityMetadata {
  @Value(
      nullable = false
  )
  private Money money;

  @Value(
      nullable = false
  )
  private SimpleEntitySecureMetadataNested nested;

  public SimpleEntityMetadata() {
    // JSON
  }

  public SimpleEntityMetadata(Money money, SimpleEntitySecureMetadataNested nested) {
    this.money = money;
    this.nested = nested;
  }

  public Money getMoney() {
    return money;
  }

  public SimpleEntitySecureMetadataNested getNested() {
    return nested;
  }

  public void validate() {
    Preconditions.checkNotNull(money, "field 'money' should not be null");
    Preconditions.checkNotNull(nested, "field 'nested' should not be null");
  }

  @Override
  public int hashCode() {
    return Objects.hash(this.money, this.nested);
  }

  @Override
  public boolean equals(Object o) {
    if (o == this) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SimpleEntityMetadata that = (SimpleEntityMetadata) o;
    return Objects.equals(money, that.money) &&
        Objects.equals(nested, that.nested);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    boolean isExactClass = this.getClass().equals(SimpleEntityMetadata.class);
    if (isExactClass) {
      sb.append("SimpleEntityMetadata {\n");
    }
    sb.append("  money: ").append(money).append("\n");
    sb.append("  nested: ").append(nested == null ? "null" : nested.toString().replaceAll("\n", "\n  ")).append("\n");
    if (isExactClass) {
      sb.append("}");
    }
    return sb.toString();
  }

  public Builder copy() {
    return builder()
      .withMoney(getMoney())
      .withNested(getNested());
  }

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder {
    @Nonnull
    private Money money = null;

    @Nonnull
    private SimpleEntitySecureMetadataNested nested = null;

    public Builder withMoney(@Nonnull Money money) {
      this.money = money;
      return this;
    }

    public Builder withNested(@Nonnull SimpleEntitySecureMetadataNested nested) {
      this.nested = nested;
      return this;
    }

    public SimpleEntityMetadata build() {
      SimpleEntityMetadata obj1 = new SimpleEntityMetadata(money, nested);
      obj1.validate();
      return obj1;
    }

    @VisibleForTesting
    public SimpleEntityMetadata buildForTesting() {
      SimpleEntityMetadata obj1 = new SimpleEntityMetadata(money, nested);
      return obj1;
    }
  }
}
