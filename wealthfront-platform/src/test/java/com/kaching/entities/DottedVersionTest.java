package com.kaching.entities;

import static com.kaching.entities.DottedVersion.dottedVersion;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertArrayEquals;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThat;

import java.util.Arrays;

import org.junit.Test;

public class DottedVersionTest {

  @Test
  public void equality_stringAndComponents() {
    DottedVersion a = dottedVersion("1.0.0");
    DottedVersion b = new DottedVersion(1, 0, 0);
    assertEquals(a, b);
  }

  @Test
  public void toString_test() {
    DottedVersion dottedVersion = dottedVersion("2015.09.15");
    assertEquals("2015.09.15", dottedVersion.toString());

    dottedVersion = new DottedVersion(2015, 9, 15);
    assertEquals("2015.9.15", dottedVersion.toString());
  }

  @Test
  public void dottedVersionForDate() {
    DottedVersion version = DottedVersion.dottedVersionForDate(20191115);
    assertThat(version.toString(), is("2019.11.15"));

    version = DottedVersion.dottedVersionForDate(20190104);
    assertThat(version.toString(), is("2019.01.04"));
  }

  @Test
  public void sorting() {
    DottedVersion[] versions = {
        new DottedVersion(1),
        new DottedVersion(0, 1),
        new DottedVersion(0, 1, 1),
        new DottedVersion(2)
    };
    DottedVersion[] expected = {
        new DottedVersion(0, 1),
        new DottedVersion(0, 1, 1),
        new DottedVersion(1),
        new DottedVersion(2)
    };
    Arrays.sort(versions);
    assertArrayEquals(expected, versions);
  }

  @Test
  public void compareTo() {
    DottedVersion version1 = dottedVersion("2020.01.01");
    DottedVersion version2 = dottedVersion("2020.1.1");
    assertEquals(version1, version2);

    version2 = dottedVersion("2020.1.2");
    assertEquals(version2.compareTo(version1), 1);

    version2 = dottedVersion("1");
    assertEquals(version2.compareTo(version1), -1);

    version2 = dottedVersion("1.1.1");
    assertEquals(version2.compareTo(version1), -1);

    version2 = dottedVersion("9999999");
    assertEquals(version2.compareTo(version1), 1);
  }

}
