package com.kaching.util.time;

import static com.google.common.collect.Lists.newArrayList;
import static com.wealthfront.test.Assert.assertThrows;
import static com.wealthfront.util.time.DateTimeZones.PT;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import java.util.ArrayList;
import java.util.Collections;

import org.joda.time.DateTime;
import org.junit.Test;

import com.kaching.platform.queryengine.exceptions.InvalidArgumentException;
import com.wealthfront.util.time.DateTimeZones;

public class YearMonthTest {

  @Test
  public void constructor() {
    YearMonth ym = new YearMonth(2001, 10);
    assertEquals(2001, ym.getYear());
    assertEquals(10, ym.getMonth());

    assertThrows(InvalidArgumentException.class, "year not in bounds: 10000",
        () -> new YearMonth(10000, 10));
    assertThrows(InvalidArgumentException.class, "month not in bounds: 13",
        () -> new YearMonth(2001, 13));
  }

  @Test
  public void isAfter() {
    YearMonth ym0 = new YearMonth(2001, 10);
    YearMonth ym1 = new YearMonth(2010, 5);
    YearMonth ym2 = new YearMonth(2010, 10);
    YearMonth ym3 = new YearMonth(2011, 1);
    YearMonth ym4 = new YearMonth(2011, 12);
    ArrayList<YearMonth> sorted = newArrayList(ym1, ym4, ym0, ym2, ym3);
    Collections.sort(sorted);
    assertEquals(ym0, sorted.get(0));
    assertEquals(ym1, sorted.get(1));
    assertEquals(ym2, sorted.get(2));
    assertEquals(ym3, sorted.get(3));
    assertEquals(ym4, sorted.get(4));
  }

  @Test
  public void isBefore() {
    YearMonth ym0 = new YearMonth(2001, 10);
    YearMonth ym1 = new YearMonth(2010, 5);
    YearMonth ym2 = new YearMonth(2010, 10);
    YearMonth ym3 = new YearMonth(2011, 1);
    assertTrue(ym0.isBefore(ym1));
    assertTrue(ym1.isBefore(ym2));
    assertTrue(ym2.isBefore(ym3));
    assertFalse(ym0.isBefore(ym0));
  }

  @Test
  public void isEqual() {
    assertEquals(new YearMonth(2001, 10), new YearMonth(2001, 10));
    assertFalse((new YearMonth(2001, 10).equals(new YearMonth(2001, 11))));
    assertFalse((new YearMonth(2001, 10).equals(new YearMonth(2010, 10))));
  }

  @Test
  public void minusMonthsShouldCarry() {
    assertEquals(new YearMonth(2001, 10), new YearMonth(2002, 2).minusMonths(4));
  }

  @Test
  public void plusMonthsShouldCarry() {
    assertEquals(new YearMonth(2001, 10), new YearMonth(2000, 2).plusMonths(20));
  }

  @Test
  public void plusMonthsNoZero() {
    assertEquals(new YearMonth(2001, 12), new YearMonth(2001, 10).plusMonths(2));
    assertEquals(new YearMonth(2002, 1), new YearMonth(2001, 10).plusMonths(3));
  }

  @Test
  public void interval() {
    assertFalse(
        new YearMonth(2004, 2).interval().contains(new DateTime(2004, 1, 31, 23, 55, 55, 999, DateTimeZones.ET)));
    assertTrue(new YearMonth(2004, 2).interval().contains(new DateTime(2004, 2, 1, 0, 0, 0, 0, DateTimeZones.ET)));
    assertTrue(
        new YearMonth(2004, 2).interval().contains(new DateTime(2004, 2, 29, 23, 55, 55, 999, DateTimeZones.ET)));
    assertFalse(new YearMonth(2004, 2).interval().contains(new DateTime(2004, 3, 1, 0, 0, 0, 0, DateTimeZones.ET)));
  }

  @Test
  public void datetimeConstructorUseET() {
    assertEquals(new YearMonth(2011, 1), new YearMonth(new DateTime(2010, 12, 31, 23, 00, 00, 00, PT)));
  }
}
