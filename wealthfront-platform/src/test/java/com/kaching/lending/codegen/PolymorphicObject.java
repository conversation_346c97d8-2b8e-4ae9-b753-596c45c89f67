package com.kaching.lending.codegen;

import java.util.Objects;

import javax.annotation.Nullable;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

import io.swagger.annotations.ApiModelProperty;

@com.fasterxml.jackson.annotation.JsonClassDescription("")
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type", visible = true, defaultImpl = PolymorphicObject.DefaultEmptyPolymorphicObject.class)
@JsonSubTypes({
  @JsonSubTypes.Type(value = PolymorphicEmptySubclassObject.class, name = "empty"),
  @JsonSubTypes.Type(value = PolymorphicRandomSubObject.class, name = "random-subobject"),
  @JsonSubTypes.Type(value = PolymorphicObject.DefaultEmptyPolymorphicObject.class, name = "default-empty"),
})
@ExposeType(value = ExposeTo.FRONTEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
public abstract class PolymorphicObject {

  @ExposeType(value = ExposeTo.FRONTEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
  public enum TypeEnum {
    RANDOM,
    EMPTY,

    @com.fasterxml.jackson.annotation.JsonEnumDefaultValue
    ENUM_DEFAULT_VALUE;

    public <T> T visit(TypeEnumVisitor<T> visitor) {
      switch (this) {
      case RANDOM:
        return visitor.caseRandom(this);
      case EMPTY:
        return visitor.caseEmpty(this);
      case ENUM_DEFAULT_VALUE:
        return visitor.caseEnumDefaultValue(this);
      default:
        throw new UnsupportedOperationException("Enum type not supported: " + this);
      }
    }

    public interface TypeEnumVisitor<T> {
    T caseRandom(TypeEnum value);

    T caseEmpty(TypeEnum value);

    T caseEnumDefaultValue(TypeEnum value);
  }

    public abstract static class DefaultTypeEnumVisitor<T> implements TypeEnumVisitor<T> {

    private final java.util.function.Supplier<T> defaultSupplier;

    public DefaultTypeEnumVisitor(T defaultValue) {
      this.defaultSupplier = () -> defaultValue;
    }

    public DefaultTypeEnumVisitor(java.util.function.Supplier<T> defaultSupplier) {
      this.defaultSupplier = defaultSupplier;
    }

    public DefaultTypeEnumVisitor(Class<? extends RuntimeException> defaultError) {
      this.defaultSupplier = () -> {
        try {
          throw defaultError.newInstance();
        } catch (InstantiationException | IllegalAccessException e) {
          throw new RuntimeException(e);
        }
      };
    }

    @Override
    public T caseRandom(TypeEnum value) {
      return defaultSupplier.get();
    }

    @Override
    public T caseEmpty(TypeEnum value) {
      return defaultSupplier.get();
    }

    @Override
    public T caseEnumDefaultValue(TypeEnum value) {
      return defaultSupplier.get();
    }
  }
  }

  @Nullable
  @JsonProperty("type")
  protected TypeEnum type = null;
  @Nullable
  @JsonProperty("id")
  protected String id = null;

  protected PolymorphicObject() {}

  protected PolymorphicObject(
    @Nullable TypeEnum type,
    @Nullable String id
  ) {
    this.type = type;
    this.id = id;
  }

  @Nullable
  @ApiModelProperty(value = "")
  public TypeEnum getType() {
    return type;
  }

  @Nullable
  @ApiModelProperty(value = "")
  public String getId() {
    return id;
  }

  String nullabilityLogString() {
    StringBuilder sb = new StringBuilder();
    sb.append("nullability log for class PolymorphicBook {");
    sb.append("type:").append(type == null ? "null" : "present").append(",");
    sb.append("id:").append(id == null ? "null" : "present").append(",");
    sb.setLength(sb.length() - 1);
    sb.append("}");
    return sb.toString();
  }

  void validate() {
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PolymorphicObject {\n");
    sb.append("    type: ").append(type).append("\n");
    sb.append("    id: ").append(id).append("\n");
    sb.append("}");
    return sb.toString();
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PolymorphicObject polymorphicObject = (PolymorphicObject) o;
    return Objects.equals(this.type, polymorphicObject.type) &&
        Objects.equals(this.id, polymorphicObject.id);
  }

  @Override
  public int hashCode() {
    return Objects.hash(type, id);
  }

  public abstract <B extends Builder<T, V>, T extends Builder<T, V>, V extends PolymorphicObject> B copy();

  public abstract static class Builder<T extends Builder<T, V>, V extends PolymorphicObject> {

    @Nullable
    protected TypeEnum type = null;
    @Nullable
    protected String id = null;

    public T type(@Nullable TypeEnum type) {
      this.type = type;
      return (T) this;
    }

    public T id(@Nullable String id) {
      this.id = id;
      return (T) this;
    }

    abstract V build();

    abstract V buildForTesting();

    @Override
    public boolean equals(Object o) {
      if (this == o) {
        return true;
      }
      if (o == null || getClass() != o.getClass()) {
        return false;
      }
      Builder builder = (Builder) o;
      return Objects.equals(this.type, builder.type) &&
          Objects.equals(this.id, builder.id);
    }

    @Override
    public int hashCode() {
      return Objects.hash(type, id);
    }

  }

  public static class DefaultEmptyPolymorphicObject extends PolymorphicObject {

    public DefaultEmptyPolymorphicObject() {}

    public DefaultEmptyPolymorphicObject(
      TypeEnum type,
      String id
    ) {
      super(type, id);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder();
      sb.append("class DefaultEmptyPolymorphicObject {\n");
      sb.append("    ").append(super.toString()).append("\n");
      sb.append("}");
      return sb.toString();
    }

    @Override
    public Builder copy() {
      return new Builder()
        .type(this.getType())
        .id(this.getId());
    }

    public static class Builder extends PolymorphicObject.Builder<Builder, PolymorphicObject> {

      @Override
      public DefaultEmptyPolymorphicObject build() {
        return new DefaultEmptyPolymorphicObject(type, id);
      }

      @Override
      public DefaultEmptyPolymorphicObject buildForTesting() {
        return new DefaultEmptyPolymorphicObject(type, id);
      }

    }

  }

  public interface PolymorphicObjectVisitor<T> {
    T visit(PolymorphicEmptySubclassObject subclassInstance);

    T visit(PolymorphicRandomSubObject subclassInstance);

    T visitDefault(PolymorphicObject instance);
  }

  public <T> T visit(PolymorphicObjectVisitor<T> visitor) {
    if (this instanceof DefaultEmptyPolymorphicObject) {
      return visitor.visitDefault(this);
    } else if (this instanceof PolymorphicEmptySubclassObject) {
      return visitor.visit((PolymorphicEmptySubclassObject) this);
    } else if (this instanceof PolymorphicRandomSubObject) {
      return visitor.visit((PolymorphicRandomSubObject) this);
    }

    return visitor.visitDefault(this);
  }

  public abstract static class DefaultPolymorphicObjectVisitor<T> implements PolymorphicObjectVisitor<T> {

    private final java.util.function.Supplier<T> defaultSupplier;

    public DefaultPolymorphicObjectVisitor(T defaultValue) {
      this.defaultSupplier = () -> defaultValue;
    }

    public DefaultPolymorphicObjectVisitor(java.util.function.Supplier<T> defaultSupplier) {
      this.defaultSupplier = defaultSupplier;
    }

    public DefaultPolymorphicObjectVisitor(Class<? extends RuntimeException> defaultError) {
      this.defaultSupplier = () -> {
        try {
          throw defaultError.newInstance();
        } catch (InstantiationException | IllegalAccessException e) {
          throw new RuntimeException(e);
        }
      };
    }
    @Override
    public T visit(PolymorphicEmptySubclassObject subclassInstance) {
      return defaultSupplier.get();
    }

    @Override
    public T visit(PolymorphicRandomSubObject subclassInstance) {
      return defaultSupplier.get();
    }

    @Override
    public T visitDefault(PolymorphicObject instance) {
      return defaultSupplier.get();
    }

  }

}

