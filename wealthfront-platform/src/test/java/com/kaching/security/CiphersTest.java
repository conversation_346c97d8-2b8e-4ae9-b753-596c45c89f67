package com.kaching.security;

import static com.google.common.collect.Sets.symmetricDifference;
import static com.kaching.security.Ciphers.VALIDATING_CIPHER_TYPES;
import static com.kaching.security.Ciphers.aes;
import static com.kaching.security.Ciphers.blowfish;
import static com.kaching.security.Ciphers.cipherGroup;
import static com.kaching.security.Ciphers.isValidatingCipher;
import static com.kaching.security.Ciphers.opensslAesCbc;
import static com.kaching.util.Base64.decode;
import static com.wealthfront.test.Assert.assertEmpty;
import static com.wealthfront.test.Assert.assertThrows;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import java.security.SecureRandom;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.junit.Test;

import com.kaching.entities.crypto.SecureHash;
import com.kaching.platform.common.Pair;

public class CiphersTest {

  private static final byte[] KEY = decode("9eqK2zJ/MFEix051k4i6jBP6AFb7gLcXQSMxND0P3wM=");
  private static final byte[] KEY2 = decode("JGndw+D90fnPFQ/KX5FI6rm/yZ43ptB0AXw53ly4q3g=");

  @Test
  public void testAes() {
    Cipher aes = aes(KEY, new SecureRandom());
    assertEquals(Aes.class, aes.getCipherClass());
    assertEquals(new SecureHash("f56d5747493ed8243d785f482e7b3c00fab63c0e9eb7a2b0f57964ec46bc7adc"), aes.getFingerprint());
    assertEncryptDecrypt(aes);
  }

  @Test
  public void testBlowfish() {
    Cipher blowfish = blowfish(KEY);
    assertEquals(Blowfish.class, blowfish.getCipherClass());
    assertEquals(new SecureHash("f56d5747493ed8243d785f482e7b3c00fab63c0e9eb7a2b0f57964ec46bc7adc"), blowfish.getFingerprint());
    assertEncryptDecrypt(blowfish);
  }

  @Test
  public void testCipherGroup() {
    Cipher cipherGroup = cipherGroup(aes(KEY, new SecureRandom()), Collections.emptyList());
    assertEquals(Aes.class, cipherGroup.getCipherClass());
    assertEquals(new SecureHash("f56d5747493ed8243d785f482e7b3c00fab63c0e9eb7a2b0f57964ec46bc7adc"), cipherGroup.getFingerprint());
    assertEncryptDecrypt(cipherGroup);
  }

  @Test
  public void testPlaintext() {
    Cipher plaintext = Ciphers.plaintext();
    assertEquals(PlaintextUnsafeCipher.class, plaintext.getCipherClass());
    assertEquals(new SecureHash("b7a3e99d15afadc79ac4bcd8fcc1d6975e5d644e24f6515ef22b3e74a2af0d80"), plaintext.getFingerprint());
    assertEncryptDecrypt(plaintext);
  }

  @Test
  public void testOpensslAesCbc() {
    Cipher opensslAesCbc = opensslAesCbc(KEY, 128, new SecureRandom());
    assertEquals(OpenSslAesCbc.class, opensslAesCbc.getCipherClass());
    assertEquals(new SecureHash("f56d5747493ed8243d785f482e7b3c00fab63c0e9eb7a2b0f57964ec46bc7adc"), opensslAesCbc.getFingerprint());
    assertEncryptDecrypt(opensslAesCbc);
  }

  @Test
  public void testValidatingCiphersValidate() {
    Map<Class<?>, Pair<Cipher, Cipher>> ciphers = Map.of(
        Aes.class, Pair.of(
            aes(KEY, new SecureRandom()),
            aes(KEY2, new SecureRandom())));
    assertEmpty(symmetricDifference(VALIDATING_CIPHER_TYPES, ciphers.keySet()));
    for (Class<?> cipherType : VALIDATING_CIPHER_TYPES) {
      Pair<Cipher, Cipher> pair = ciphers.get(cipherType);
      assertTrue(isValidatingCipher(cipherType));
      assertEquals(cipherType, pair.left.getCipherClass());
      assertEquals(cipherType, pair.right.getCipherClass());
      assertCipherValidates(pair.left, pair.right);
    }
  }

  @Test
  public void testBlowfishIsNotAValidatingCipher() {
    assertFalse(isValidatingCipher(blowfish(KEY)));
  }

  @Test
  public void testOpensslAesCbcIsNotAValidatingCipher() {
    /* OpensslAesCbc will sometimes not correctly throw when it decrypts a value that it shouldn't be able to read.
     * Putting it as an option in testValidatingCiphersValidate() and running it in a loop will pretty quickly
     * fail as a flaky test.  It's not trustworthy as a validating cipher.
     */
    assertFalse(isValidatingCipher(opensslAesCbc(KEY, 128, new SecureRandom())));
  }

  @Test
  public void testCannotMixValidatingAndNonValidating() {
    assertThrows(IllegalArgumentException.class, () -> cipherGroup(aes(KEY, new SecureRandom()), List.of(blowfish(KEY))));
    assertThrows(IllegalArgumentException.class, () -> cipherGroup(blowfish(KEY), List.of(aes(KEY, new SecureRandom()))));
  }

  private static void assertCipherValidates(Cipher left, Cipher right) {
    String plaintext = "testmsg!";
    byte[] plaintextBytes = plaintext.getBytes();
    assertEquals(plaintext, new String(left.decrypt(left.encrypt(plaintextBytes))));
    assertEquals(plaintext, new String(right.decrypt(right.encrypt(plaintextBytes))));
    assertThrows(RuntimeException.class, () -> left.decrypt(right.encrypt(plaintextBytes)));
    assertThrows(RuntimeException.class, () -> right.decrypt(left.encrypt(plaintextBytes)));
  }

  private static void assertEncryptDecrypt(Cipher cipher) {
    assertTrue("Error during encryption/decryption",
        "message!".equals(new String(cipher.decrypt(cipher.encrypt("message!".getBytes())))));
  }

}

