package com.kaching.security.securitymanager;

import static com.wealthfront.test.Assert.assertThrows;
import static java.util.Arrays.asList;
import static java.util.Collections.emptyList;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import java.io.FilePermission;
import java.net.SocketPermission;
import java.net.UnknownHostException;
import java.security.Permission;
import java.util.List;
import java.util.Map;

import org.junit.Test;

import com.kaching.platform.discovery.Manifest;
import com.kaching.platform.guice.KachingServices.FBANK;
import com.kaching.platform.guice.KachingServices.UM;

public class FakeServiceSecurityManagerTest {

  @Test(expected = SecurityException.class)
  public void checkSocketPermission() {
    FakeServiceSecurityManager.builder()
        .build()
        .checkPermission(new SocketPermission("127.0.0.1", "connect"));
  }

  @Test(expected = SecurityException.class)
  public void checkSocketPermission_Deny() {
    FakeServiceSecurityManager.builder()
        .socketPermission(p -> false)
        .build()
        .checkPermission(new SocketPermission("127.0.0.1", "connect"));
  }

  @Test()
  public void checkSocketPermission_Allow() {
    FakeServiceSecurityManager.builder()
        .socketPermission(p -> false)
        .socketPermission(p -> true)
        .build()
        .checkPermission(new SocketPermission("127.0.0.1", "connect"));
  }

  @Test(expected = SecurityException.class)
  public void checkFilePermission() {
    FakeServiceSecurityManager.builder()
        .build()
        .checkPermission(new FilePermission("<<ALL FILES>>", "read"));
  }

  @Test(expected = SecurityException.class)
  public void checkFilePermission_Deny() {
    FakeServiceSecurityManager.builder()
        .filePermission(p -> false)
        .build()
        .checkPermission(new FilePermission("<<ALL FILES>>", "read"));
  }

  @Test
  public void checkFilePermission_Allow() {
    FakeServiceSecurityManager.builder()
        .filePermission(p -> false)
        .filePermission(p -> true)
        .build()
        .checkPermission(new FilePermission("<<ALL FILES>>", "read"));
  }

  @Test
  public void executeWithPermission() {
    FakeServiceSecurityManager manager = FakeServiceSecurityManager.builder().build();
    SocketPermission permission = new SocketPermission("127.0.0.1", "connect");
    assertEquals(0, manager.getThreadLocalPermissions().size());
    assertThrows(SecurityException.class, () -> manager.checkPermission(permission));
    manager.executeWithPermission(permission).run(() -> manager.checkPermission(permission));
    assertEquals(0, manager.getThreadLocalPermissions().size());
    assertThrows(RuntimeException.class, () -> manager.executeWithPermission(permission).run(() -> {
      throw new RuntimeException();
    }));
    assertEquals(0, manager.getThreadLocalPermissions().size());
    assertThrows(SecurityException.class, () -> manager.checkPermission(permission));

    assertThrows(SecurityException.class, () ->
        manager.executeWithPermission()
            .run(() -> manager.checkPermission(permission)));

    assertTrue(manager.executeWithPermission(permission).get(() -> {
      manager.checkPermission(permission);
      return true;
    }));

    assertTrue(manager.executeWithPermission((List<Permission>) null).get(() -> true));

    assertEquals(0, manager.getThreadLocalPermissions().size());
    assertNull(manager.getThreadLocalPermissions().get(permission));
    manager.executeWithPermission(permission)
      .run(() -> {
        assertEquals(1, manager.getThreadLocalPermissions().get(permission).intValue());
        manager.executeWithPermission(permission).run(() -> {
          assertEquals(2, manager.getThreadLocalPermissions().get(permission).intValue());
        });
        assertEquals(1, manager.getThreadLocalPermissions().get(permission).intValue());
      });
    assertEquals(0, manager.getThreadLocalPermissions().size());
    assertNull(manager.getThreadLocalPermissions().get(permission));

    FakeServiceSecurityManager.PermissionContext c = manager.executeWithPermission(permission);
    c.run(() -> assertThrows(IllegalStateException.class, () -> c.run(() -> {})));
  }

  @Test
  public void getThreadLocalPermissions() {
    FakeServiceSecurityManager manager = FakeServiceSecurityManager.builder().build();
    SocketPermission permission1 = new SocketPermission("127.0.0.1", "connect");
    SocketPermission permission2 = new SocketPermission("127.0.0.1", "connect");
    assertEquals(permission1, permission2);
    Map<Permission, Integer> set = manager.getThreadLocalPermissions();
    assertEquals(0, set.size());
    set.put(permission1, 1);
    set.put(permission2, 1);
    assertEquals(2, set.size());
    set.remove(permission2);
    assertEquals(1, set.size());
    set.remove(permission1);
    assertEquals(0, set.size());
  }

  @Test
  public void checkPermission_socketPermissionToLocalhost() {
    FakeServiceSecurityManager securityManager = FakeServiceSecurityManager.builder()
        .socketPermission(new AllowLocalHostPredicate())
        .build();
    securityManager.checkPermission(new SocketPermission("127.0.0.1", "connect"));
    securityManager.checkPermission(new SocketPermission("localhost", "connect"));

    Object context = new Object();
    securityManager.checkPermission(new SocketPermission("127.0.0.1", "connect"), context);
    securityManager.checkPermission(new SocketPermission("localhost", "connect"), context);
  }

  @Test
  public void checkPermission_socketPermissionToNonLocalhost() {
    FakeServiceSecurityManager securityManager = FakeServiceSecurityManager.builder()
        .socketPermission(new AllowLocalHostPredicate())
        .build();
    assertThrows(SecurityException.class,
        () -> securityManager.checkPermission(new SocketPermission("*******", "connect")));
    assertThrows(SecurityException.class,
        () -> securityManager.checkPermission(new SocketPermission("*******", "connect"), new Object()));
  }

  @Test
  public void checkPermission_HostNotInManifest() throws UnknownHostException {
    Manifest manifest = new Manifest(emptyList());
    FakeServiceSecurityManager securityManager = FakeServiceSecurityManager.builder()
        .socketPermission(new AllowedServicesPredicate(manifest, asList(FBANK.class, UM.class)))
        .build();
    assertThrows(SecurityException.class, () ->
        securityManager.checkPermission(new SocketPermission("*******:9031", "connect")));
  }

}
