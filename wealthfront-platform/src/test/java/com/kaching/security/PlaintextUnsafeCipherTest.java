package com.kaching.security;

import static org.junit.Assert.assertArrayEquals;
import static org.junit.Assert.assertEquals;

import org.junit.Test;

public class PlaintextUnsafeCipherTest {

  @Test
  public void testEncryptOntoBytes() {
    assertArrayEquals("foo".getBytes(), new PlaintextUnsafeCipher().encryptOntoBytes("foo".getBytes()));
  }

  @Test
  public void testEncrypt() {
    assertEquals("foo", new PlaintextUnsafeCipher().encrypt("foo".getBytes()));
  }

  @Test
  public void testDecrypt() {
    assertArrayEquals("foo".getBytes(), new PlaintextUnsafeCipher().decrypt("foo"));
  }

  @Test
  public void testTestDecryptBytes() {
    assertArrayEquals("foo".getBytes(), new PlaintextUnsafeCipher().decrypt("foo".getBytes()));
  }

}