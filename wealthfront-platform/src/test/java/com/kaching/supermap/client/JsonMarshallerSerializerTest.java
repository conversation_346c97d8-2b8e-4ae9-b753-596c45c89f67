package com.kaching.supermap.client;

import static com.google.common.base.Charsets.UTF_8;
import static org.junit.Assert.assertEquals;

import org.junit.Before;
import org.junit.Test;

import com.twolattes.json.Entity;
import com.twolattes.json.Value;

import voldemort.serialization.Serializer;
import voldemort.serialization.SerializerDefinition;

public class JsonMarshallerSerializerTest {

  private Serializer<Object> serializer;

  @Before
  @SuppressWarnings("unchecked")
  public void before() {
    KachingSerializerFactory factory = new KachingSerializerFactory();
    serializer = (Serializer<Object>) factory.getSerializer(new SerializerDefinition(
        "json-marshaller",
        "java=com.kaching.supermap.client.JsonMarshallerSerializerTest$MyEntity"));
  }

  @Test
  public void toBytes() {
    byte[] bytes = serializer.toBytes(new MyEntity("bar"));
    assertEquals("{\"foo\":\"bar\"}", new String(bytes, UTF_8));
  }

  @Test
  public void toObject() {
    byte[] bytes = serializer.toBytes(new MyEntity("bar"));
    MyEntity myEntity = (MyEntity) serializer.toObject(bytes);
    assertEquals("bar", myEntity.foo);
  }

  @Entity
  static class MyEntity {

    @Value String foo;

    @SuppressWarnings("unused")
    private MyEntity() {}

    MyEntity(String foo) {
      this.foo = foo;
    }

  }

}
