package com.kaching.platform.monitoring;

import static org.junit.Assert.assertEquals;

import org.junit.Test;

import com.kaching.platform.guice.Revision;

public class GetRevisionTest {

  @Test
  public void process() {
    assertEquals("revision", getQuery(new Revision("revision")).process());
  }

  private GetRevision getQuery(Revision revision) {
    GetRevision query = new GetRevision();
    query.revision = revision;
    return query;
  }

}