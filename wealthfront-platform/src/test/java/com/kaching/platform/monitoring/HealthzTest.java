package com.kaching.platform.monitoring;

import static com.kaching.platform.monitoring.HealthzReport.MARSHALLER;
import static com.wealthfront.test.Assert.assertMarshalling;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.junit.After;
import org.junit.Test;

import com.kaching.platform.discovery.ServiceId;
import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.guice.Revision;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.QueryRuntimeMonitor;
import com.kaching.platform.queryengine.ServiceRevision;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.WExpectations;
import com.twolattes.json.Json;

public class HealthzTest {

  private final Mockeries.WFMockery mockery = Mockeries.mockery(true);
  private final QueryRuntimeMonitor queryRuntimeMonitor = mockery.mock(QueryRuntimeMonitor.class);
  private final RollingStatistics rollingStatistics1 = mockery.mock(RollingStatistics.class, "1");
  private final RollingStatistics rollingStatistics2 = mockery.mock(RollingStatistics.class, "2");

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void process() {
    mockery.checking(new WExpectations() {{
      oneOf(queryRuntimeMonitor).statistics();
      will(returnList(Map.entry(QueryA.class.getSimpleName(), rollingStatistics1)));

      oneOf(rollingStatistics1).rollingAverage();
      will(returnValue(200.0));
      oneOf(rollingStatistics1).rollingMedian();
      will(returnValue(195));
      oneOf(rollingStatistics1).rollingMin();
      will(returnValue(50));
      oneOf(rollingStatistics1).rollingMax();
      will(returnValue(250));
      oneOf(rollingStatistics1).rollingTotalCount();
      will(returnValue(20));
      oneOf(rollingStatistics1).rollingFailurePercentage();
      will(returnValue(0.0));
      oneOf(rollingStatistics1).rollingInvalidPercentage();
      will(returnValue(0.0));

      ConcurrentHashMap<String, RollingStatistics> queryStats = new ConcurrentHashMap<>();
      queryStats.put(QueryB.class.getSimpleName(), rollingStatistics2);
      queryStats.put(QueryB.class.getSimpleName(), rollingStatistics2);
      oneOf(queryRuntimeMonitor).downstreamStatistics();
      will(returnList(Map.entry(
          new ServiceRevision(ServiceId.of("bank1"), KachingServices.BANK.class, new Revision("abc123")),
          queryStats)));

      oneOf(rollingStatistics2).rollingAverage();
      will(returnValue(100.0));
      oneOf(rollingStatistics2).rollingMedian();
      will(returnValue(95));
      oneOf(rollingStatistics2).rollingMin();
      will(returnValue(50));
      oneOf(rollingStatistics2).rollingMax();
      will(returnValue(150));
      oneOf(rollingStatistics2).rollingTotalCount();
      will(returnValue(10));
      oneOf(rollingStatistics2).rollingFailurePercentage();
      will(returnValue(0.6));
      oneOf(rollingStatistics2).rollingInvalidPercentage();
      will(returnValue(0.2));
    }});

    Json.Object expected = Json.object(
        "stackTraces", Json.array(),
        "statistics", Json.object(
            "QueryA", Json.object(
                "average", 200.0,
                "median", 195,
                "min", 50,
                "max", 250,
                "count", 20,
                "failedQueries", 0.0,
                "invalidQueries", 0.0
            )
        ),
        "downstreamReports", Json.array(
            Json.object(
                "serviceRevision", Json.object(
                    "id", "bank1",
                    "kind", "com.kaching.platform.guice.KachingServices$BANK",
                    "revision", "abc123"
                ),
                "statistics", Json.object(
                    "QueryB", Json.object(
                        "average", 100.0,
                        "median", 95,
                        "min", 50,
                        "max", 150,
                        "count", 10,
                        "failedQueries", 0.6,
                        "invalidQueries", 0.2
                    )
                )
            )
        ));
    HealthzReport report = getQuery().process();
    assertMarshalling(MARSHALLER, expected, report);
  }

  private Healthz getQuery() {
    Healthz query = new Healthz();
    query.monitor = queryRuntimeMonitor;
    return query;
  }

  private static class QueryA extends AbstractQuery<Boolean> {

    @Override
    public Boolean process() {
      return true;
    }

  }

  private static class QueryB extends AbstractQuery<String> {

    @Override
    public String process() {
      return "David Lakata";
    }

  }

}