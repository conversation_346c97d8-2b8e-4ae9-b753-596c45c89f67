package com.kaching.platform.monitoring.icinga;

import static com.wealthfront.util.time.DateTimeZones.ET;
import static org.junit.Assert.assertEquals;

import java.sql.SQLException;

import org.joda.time.DateTime;
import org.junit.After;
import org.junit.Test;

import com.google.inject.util.Providers;
import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.hibernate.C3P0PooledDataSourceFetcher;
import com.kaching.platform.hibernate.StubPooledDataSource;
import com.kaching.platform.monitoring.RunningQueriesSummarizer;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.Mockeries.WFMockery;
import com.kaching.platform.testing.WExpectations;

public class CheckC3P0ConnectionPoolLoadTest {

  private final WFMockery mockery = Mockeries.mockery(true);
  private final C3P0PooledDataSourceFetcher c3P0PooledDataSourceFetcherMock =
      mockery.mock(C3P0PooledDataSourceFetcher.class);
  private final RunningQueriesSummarizer runningQueriesSummarizerMock = mockery.mock(RunningQueriesSummarizer.class);
  private DateTime now = new DateTime(2023, 11, 15, 1, 0, 0, ET);

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void process_nonExistentConnectionPool_returnsOKAY() {
    mockery.checking(new WExpectations() {{
      never(runningQueriesSummarizerMock).getSummaryText(with(any(RunningQueriesSummarizer.SortType.class)));
      oneOf(c3P0PooledDataSourceFetcherMock).get();
      will(returnNone());
    }});
    CheckC3P0ConnectionPoolLoad check = createCheck();
    IcingaOutput output = check.getIcingaOutput();
    assertEquals(IcingaOutput.ExitCode.OKAY, output.getExitCode());
    assertEquals("Service does not have a database connection pool", output.getMessage());
  }

  @Test
  public void process_nonOverloadedConnectionPool_returnsOKAY() {
    StubPooledDataSource pooledDataSource = new StubPooledDataSource("identityToken") {
      @Override
      public int getNumThreadsAwaitingCheckoutDefaultUser() throws SQLException {
        return 0;
      }

      @Override
      public int getNumBusyConnectionsAllUsers() throws SQLException {
        return 0;
      }

      @Override
      public int getNumIdleConnectionsAllUsers() throws SQLException {
        return 0;
      }

      @Override
      public int getNumUnclosedOrphanedConnectionsAllUsers() throws SQLException {
        return 0;
      }
    };
    mockery.checking(new WExpectations() {{
      oneOf(c3P0PooledDataSourceFetcherMock).get();
      will(returnOption(pooledDataSource));
    }});
    CheckC3P0ConnectionPoolLoad check = createCheck();
    IcingaOutput output = check.getIcingaOutput();
    assertEquals(IcingaOutput.ExitCode.OKAY, output.getExitCode());
    assertEquals(
        "There are 0 busy connections, 0 idle connections, 0 unclosed orphaned connections, and 0 threads waiting for a database connection. " +
            "The threshold on waiting threads is 50.",
        output.getMessage());
  }

  @Test
  public void process_nonOverloadedConnectionPool_returnsOKAYCallsRunningQueriesSummarizer() {
    StubPooledDataSource pooledDataSource = new StubPooledDataSource("identityToken") {
      @Override
      public int getNumThreadsAwaitingCheckoutDefaultUser() throws SQLException {
        return 6;
      }

      @Override
      public int getNumBusyConnectionsAllUsers() throws SQLException {
        return 1;
      }

      @Override
      public int getNumIdleConnectionsAllUsers() throws SQLException {
        return 2;
      }

      @Override
      public int getNumUnclosedOrphanedConnectionsAllUsers() throws SQLException {
        return 3;
      }
    };
    mockery.checking(new WExpectations() {{
      oneOf(runningQueriesSummarizerMock).getSummaryText(RunningQueriesSummarizer.SortType.time);
      will(returnValue("Top output"));
      oneOf(c3P0PooledDataSourceFetcherMock).get();
      will(returnOption(pooledDataSource));
    }});
    CheckC3P0ConnectionPoolLoad check = createCheck();
    IcingaOutput output = check.getIcingaOutput();
    assertEquals(IcingaOutput.ExitCode.OKAY, output.getExitCode());
    assertEquals(
        "There are 1 busy connections, 2 idle connections, 3 unclosed orphaned connections, and 6 threads waiting for a database connection. " +
            "The threshold on waiting threads is 50.",
        output.getMessage());
  }

  @Test
  public void process_overloadedConnectionPool_returnsCRITICAL() {
    StubPooledDataSource pooledDataSource = new StubPooledDataSource("identityToken") {
      @Override
      public int getNumBusyConnectionsAllUsers() throws SQLException {
        return 15;
      }

      @Override
      public int getNumThreadsAwaitingCheckoutDefaultUser() throws SQLException {
        return 100;
      }

      @Override
      public int getNumIdleConnectionsAllUsers() throws SQLException {
        return 0;
      }

      @Override
      public int getNumUnclosedOrphanedConnectionsAllUsers() throws SQLException {
        return 0;
      }
    };
    mockery.checking(new WExpectations() {{
      oneOf(c3P0PooledDataSourceFetcherMock).get();
      will(returnOption(pooledDataSource));
      oneOf(runningQueriesSummarizerMock).getSummaryText(RunningQueriesSummarizer.SortType.time);
      will(returnValue("Top output"));
    }});
    CheckC3P0ConnectionPoolLoad check = createCheck();
    check.serviceKind = new KachingServices.FAKEAPI();
    IcingaOutput output = check.getIcingaOutput();
    assertEquals(IcingaOutput.ExitCode.CRITICAL, output.getExitCode());
    assertEquals(
        "There are 15 busy connections, 0 idle connections, 0 unclosed orphaned connections, and 100 threads waiting for a database connection. " +
            "The threshold on waiting threads is 50. " +
            "Please check the logs for a list of queries currently running as PagerDuty will truncate output.",
        output.getMessage());
  }

  @Test
  public void process_overloadedConnectionPool_serviceWithLowLatencyRequirementsOutsidePagingeHours_returnsWARNING() {
    StubPooledDataSource pooledDataSource = new StubPooledDataSource("identityToken") {
      @Override
      public int getNumBusyConnectionsAllUsers() throws SQLException {
        return 15;
      }

      @Override
      public int getNumThreadsAwaitingCheckoutDefaultUser() throws SQLException {
        return 100;
      }

      @Override
      public int getNumIdleConnectionsAllUsers() throws SQLException {
        return 0;
      }

      @Override
      public int getNumUnclosedOrphanedConnectionsAllUsers() throws SQLException {
        return 0;
      }
    };

    String alertMessage = "There are 15 busy connections, 0 idle connections, 0 unclosed orphaned connections, and 100 threads waiting for a database connection. " +
        "The threshold on waiting threads is 50. " +
        "Please check the logs for a list of queries currently running as PagerDuty will truncate output.";
    mockery.checking(new WExpectations() {{
      oneOf(c3P0PooledDataSourceFetcherMock).get();
      will(returnOption(pooledDataSource));
      oneOf(runningQueriesSummarizerMock).getSummaryText(RunningQueriesSummarizer.SortType.time);
      will(returnValue("Top output"));
    }});
    CheckC3P0ConnectionPoolLoad check = createCheck();
    check.serviceKind = new KachingServices.BI();
    IcingaOutput output = check.getIcingaOutput();
    assertEquals(IcingaOutput.ExitCode.WARNING, output.getExitCode());
    assertEquals(alertMessage, output.getMessage());
  }

  @Test
  public void process_overloadedConnectionPool_serviceWithLowLatencyRequirementsInsidePagingeHours_returnsCRITICAL() {
    StubPooledDataSource pooledDataSource = new StubPooledDataSource("identityToken") {
      @Override
      public int getNumBusyConnectionsAllUsers() throws SQLException {
        return 15;
      }

      @Override
      public int getNumThreadsAwaitingCheckoutDefaultUser() throws SQLException {
        return 100;
      }

      @Override
      public int getNumIdleConnectionsAllUsers() throws SQLException {
        return 0;
      }

      @Override
      public int getNumUnclosedOrphanedConnectionsAllUsers() throws SQLException {
        return 0;
      }
    };

    String alertMessage = "There are 15 busy connections, 0 idle connections, 0 unclosed orphaned connections, and 100 threads waiting for a database connection. " +
        "The threshold on waiting threads is 50. " +
        "Please check the logs for a list of queries currently running as PagerDuty will truncate output.";
    mockery.checking(new WExpectations() {{
      oneOf(c3P0PooledDataSourceFetcherMock).get();
      will(returnOption(pooledDataSource));
      oneOf(runningQueriesSummarizerMock).getSummaryText(RunningQueriesSummarizer.SortType.time);
      will(returnValue("Top output"));
    }});
    now = new DateTime(2023, 11, 15, 9, 35, 0, ET);
    CheckC3P0ConnectionPoolLoad check = createCheck();
    check.serviceKind = new KachingServices.BI();
    IcingaOutput output = check.getIcingaOutput();
    assertEquals(IcingaOutput.ExitCode.CRITICAL, output.getExitCode());
    assertEquals(alertMessage, output.getMessage());
  }

  @Test
  public void process_poolThrowsException_returnsCRITICAL() {
    StubPooledDataSource pooledDataSource = new StubPooledDataSource("identityToken") {
      @Override
      public int getNumThreadsAwaitingCheckoutDefaultUser() throws SQLException {
        throw new SQLException("Unimplemented!");
      }
    };
    mockery.checking(new WExpectations() {{
      oneOf(c3P0PooledDataSourceFetcherMock).get();
      will(returnOption(pooledDataSource));
    }});
    CheckC3P0ConnectionPoolLoad check = createCheck();
    IcingaOutput output = check.getIcingaOutput();
    assertEquals(IcingaOutput.ExitCode.CRITICAL, output.getExitCode());
    assertEquals("Received SQLException with message: Unimplemented!", output.getMessage());
  }

  private CheckC3P0ConnectionPoolLoad createCheck() {
    CheckC3P0ConnectionPoolLoad check = new CheckC3P0ConnectionPoolLoad();
    check.c3P0PooledDataSourceFetcher = c3P0PooledDataSourceFetcherMock;
    check.runningQueriesSummarizer = runningQueriesSummarizerMock;
    check.clock = Providers.of(now);
    return check;
  }

}