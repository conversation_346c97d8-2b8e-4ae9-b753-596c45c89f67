package com.kaching.platform.zk.degraded;

import static com.kaching.platform.zk.degraded.DegradedModeStatus.TrafficRestrictionLevel.NONE;
import static com.kaching.platform.zk.degraded.DegradedModeStatus.inactiveDegradedModeStatus;
import static com.wealthfront.test.Assert.assertNotEmpty;
import static org.junit.Assert.assertEquals;

import java.time.Duration;

import org.junit.After;
import org.junit.Test;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.kaching.platform.queryengine.FakeStackTraceMonitor;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.WExpectations;
import com.kaching.util.tests.FakeTicker;

public class CachedDegradedModeStatusProviderImplTest {

  private final Mockeries.WFMockery mockery = Mockeries.mockery();
  private final DegradedModeStatusProvider degradedModeStatusProvider = mockery.mock(DegradedModeStatusProvider.class);
  private final FakeStackTraceMonitor stm = new FakeStackTraceMonitor();

  @After
  public void assertIsSatisfied() {
    mockery.assertIsSatisfied();
    stm.assertNoUnexpectedExceptions();
  }

  @Test
  public void getApiDegradedModeStatus_getSfeDegradedModeStatus_noCacheRefreshes() {
    DegradedModeStatus degradedModeStatus1 = new DegradedModeStatus(true, NONE);
    DegradedModeStatus degradedModeStatus2 = new DegradedModeStatus(false, NONE);

    mockery.checking(new WExpectations() {{
      oneOf(degradedModeStatusProvider).getApiDegradedModeStatus();
      will(returnValue(degradedModeStatus1));

      oneOf(degradedModeStatusProvider).getSfeDegradedModeStatus();
      will(returnValue(degradedModeStatus2));
    }});

    CachedDegradedModeStatusProviderImpl provider = setupProvider(new CachedDegradedModeStatusProviderImpl() {
      @Override
      Duration getCacheRefreshInterval() {
        return Duration.ofMinutes(10);
      }
    });

    assertEquals(degradedModeStatus1, provider.getApiDegradedModeStatus());
    assertEquals(degradedModeStatus1, provider.getApiDegradedModeStatus());
    assertEquals(degradedModeStatus1, provider.getApiDegradedModeStatus());
    assertEquals(degradedModeStatus1, provider.getApiDegradedModeStatus());

    assertEquals(degradedModeStatus2, provider.getSfeDegradedModeStatus());
    assertEquals(degradedModeStatus2, provider.getSfeDegradedModeStatus());
    assertEquals(degradedModeStatus2, provider.getSfeDegradedModeStatus());
    assertEquals(degradedModeStatus2, provider.getSfeDegradedModeStatus());
  }

  @Test
  public void getApiDegradedModeStatus_withRefreshes() {
    DegradedModeStatus degradedModeStatus1 = new DegradedModeStatus(true, NONE);
    DegradedModeStatus degradedModeStatus2 = new DegradedModeStatus(false, NONE);

    Duration cacheRefreshInterval = Duration.ofSeconds(10);
    Duration longerThanCacheRefreshInterval = Duration.ofSeconds(11);
    FakeTicker ticker = new FakeTicker();

    mockery.checking(new WExpectations() {{
      oneOf(degradedModeStatusProvider).getApiDegradedModeStatus();
      will(returnValue(degradedModeStatus1));

      oneOf(degradedModeStatusProvider).getApiDegradedModeStatus();
      will(returnValue(degradedModeStatus2));

      oneOf(degradedModeStatusProvider).getApiDegradedModeStatus();
      will(throwException(new RuntimeException("An exception appeared")));
    }});

    CachedDegradedModeStatusProviderImpl provider = setupProvider(new CachedDegradedModeStatusProviderImpl() {
      @Override
      LoadingCache<Type, DegradedModeStatus> getCache() {
        return CacheBuilder.newBuilder()
            .refreshAfterWrite(cacheRefreshInterval)
            .ticker(ticker)
            .build(new CacheLoader<Type, DegradedModeStatus>() {
              @Override
              public DegradedModeStatus load(Type type) {
                return fetchDegradedModeStatus(type);
              }

              @Override
              public ListenableFuture<DegradedModeStatus> reload(Type type, DegradedModeStatus oldVal) {
                return Futures.immediateFuture(fetchDegradedModeStatus(type));
              }
            });
      }

      @Override
      Duration getCacheRefreshInterval() {
        return cacheRefreshInterval;
      }
    });

    assertEquals(degradedModeStatus1, provider.getApiDegradedModeStatus());

    ticker.advance(longerThanCacheRefreshInterval);
    assertEquals(degradedModeStatus2, provider.getApiDegradedModeStatus());
    stm.assertNoUnexpectedExceptions();

    ticker.advance(longerThanCacheRefreshInterval);
    assertEquals(inactiveDegradedModeStatus(), provider.getApiDegradedModeStatus());
    stm.assertOnlyStackTraceAndClear("An exception appeared");
  }

  @Test
  public void getSfeDegradedModeStatus_withRefreshes() {
    DegradedModeStatus degradedModeStatus1 = new DegradedModeStatus(true, NONE);
    DegradedModeStatus degradedModeStatus2 = new DegradedModeStatus(false, NONE);

    Duration cacheRefreshInterval = Duration.ofSeconds(10);
    Duration longerThanCacheRefreshInterval = Duration.ofSeconds(11);
    FakeTicker ticker = new FakeTicker();

    mockery.checking(new WExpectations() {{
      oneOf(degradedModeStatusProvider).getSfeDegradedModeStatus();
      will(returnValue(degradedModeStatus1));

      oneOf(degradedModeStatusProvider).getSfeDegradedModeStatus();
      will(returnValue(degradedModeStatus2));

      oneOf(degradedModeStatusProvider).getSfeDegradedModeStatus();
      will(throwException(new RuntimeException("An exception appeared")));
    }});

    CachedDegradedModeStatusProviderImpl provider = setupProvider(new CachedDegradedModeStatusProviderImpl() {
      @Override
      LoadingCache<Type, DegradedModeStatus> getCache() {
        return CacheBuilder.newBuilder()
            .refreshAfterWrite(cacheRefreshInterval)
            .ticker(ticker)
            .build(new CacheLoader<Type, DegradedModeStatus>() {
              @Override
              public DegradedModeStatus load(Type type) {
                return fetchDegradedModeStatus(type);
              }

              @Override
              public ListenableFuture<DegradedModeStatus> reload(Type type, DegradedModeStatus oldVal) {
                return Futures.immediateFuture(fetchDegradedModeStatus(type));
              }
            });
      }

      @Override
      Duration getCacheRefreshInterval() {
        return cacheRefreshInterval;
      }
    });

    assertEquals(degradedModeStatus1, provider.getSfeDegradedModeStatus());

    ticker.advance(longerThanCacheRefreshInterval);
    assertEquals(degradedModeStatus2, provider.getSfeDegradedModeStatus());
    stm.assertNoUnexpectedExceptions();

    ticker.advance(longerThanCacheRefreshInterval);
    assertEquals(inactiveDegradedModeStatus(), provider.getSfeDegradedModeStatus());

    stm.assertOnlyStackTraceAndClear("An exception appeared");
  }

  @Test
  public void getDegradedModeStatus_throws_returnsInactiveDegradedModeByDefault() {
    mockery.checking(new WExpectations() {{
      oneOf(degradedModeStatusProvider).getApiDegradedModeStatus();
      will(throwException(new RuntimeException()));

      oneOf(degradedModeStatusProvider).getSfeDegradedModeStatus();
      will(throwException(new RuntimeException()));
    }});

    CachedDegradedModeStatusProviderImpl provider = getProvider();
    assertEquals(inactiveDegradedModeStatus(), provider.getApiDegradedModeStatus());
    assertEquals(inactiveDegradedModeStatus(), provider.getSfeDegradedModeStatus());
    assertNotEmpty(stm.getStackTracesAndClear());
  }

  @Test
  public void getDegradedModeStatus() {
    DegradedModeStatus degradedModeStatus1 = new DegradedModeStatus(true, NONE);
    DegradedModeStatus degradedModeStatus2 = new DegradedModeStatus(false, NONE);

    mockery.checking(new WExpectations() {{
      oneOf(degradedModeStatusProvider).getApiDegradedModeStatus();
      will(returnValue(degradedModeStatus1));

      oneOf(degradedModeStatusProvider).getSfeDegradedModeStatus();
      will(returnValue(degradedModeStatus2));
    }});

    CachedDegradedModeStatusProviderImpl provider = getProvider();
    assertEquals(degradedModeStatus1, provider.getApiDegradedModeStatus());
    assertEquals(degradedModeStatus2, provider.getSfeDegradedModeStatus());
  }

  private CachedDegradedModeStatusProviderImpl setupProvider(CachedDegradedModeStatusProviderImpl provider) {
    provider.degradedModeStatusProvider = degradedModeStatusProvider;
    provider.stm = stm;
    return provider;
  }

  private CachedDegradedModeStatusProviderImpl getProvider() {
    return setupProvider(new CachedDegradedModeStatusProviderImpl());
  }

}
