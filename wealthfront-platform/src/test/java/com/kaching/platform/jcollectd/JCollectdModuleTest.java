package com.kaching.platform.jcollectd;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import java.util.Set;

import org.collectd.mx.MBeanCollector;
import org.collectd.mx.MBeanSender;
import org.junit.Test;

import com.google.inject.AbstractModule;
import com.google.inject.Guice;
import com.google.inject.Injector;
import com.google.inject.name.Names;
import com.wealthfront.test.AllowLocalFileAccess;

@AllowLocalFileAccess(paths = {"jcd.properties"})
public class JCollectdModuleTest {

  private static final String TEMPLATE_NAME = "test";
  private static final String DEST = "udp://239.192.74.66:25826";

  @Test
  public void sender() throws Exception {
    JCollectdModule module = new JCollectdModule("test", "bi0", true) {
      @Override
      MBeanSender getSender(
          String destination,
          Set<String> templates,
          Set<MBeanCollector> collectors) {
        assertEquals(DEST, destination);
        assertTrue(templates.contains("javalang"));
        assertTrue(templates.contains(TEMPLATE_NAME));
        return super.getSender(destination, templates, collectors);
      }
    };

    Injector injector = Guice.createInjector(module, new AbstractModule() {
      @Override
      protected void configure() {
        bind(String.class)
            .annotatedWith(Names.named("jcollectd.dest"))
            .toInstance(DEST);

        JCollectdModule.bindTemplate(binder(), TEMPLATE_NAME);
      }
    });

    MBeanSender sender = null;
    try {
      sender = injector.getInstance(MBeanSender.class);
      assertEquals("test", sender.getInstanceName());
    } finally {
      if (sender != null) {
        sender.shutdown();
      }
    }
  }
}
