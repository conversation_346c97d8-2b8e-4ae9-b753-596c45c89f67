package com.kaching.platform.queryengine;

import static com.google.common.base.Preconditions.checkNotNull;
import static com.google.common.collect.Lists.newArrayList;
import static com.google.common.collect.Maps.newHashMap;
import static com.google.common.collect.Sets.newHashSet;
import static com.kaching.platform.common.logging.Log.getLog;
import static com.kaching.platform.guice.KcHibernateModule.DB_SESSION_KEY;
import static com.kaching.platform.guice.KcHibernateModule.DB_SESSION_KIND_KEY;
import static com.kaching.platform.guice.KcHibernateModule.SESSION_KEY;
import static com.kaching.platform.queryengine.InjectorTester.Mode.EXTENSIVE;
import static java.lang.String.format;

import java.io.OutputStream;
import java.io.PrintWriter;
import java.io.Serializable;
import java.io.StringWriter;
import java.lang.annotation.Annotation;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Member;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.sql.Connection;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import org.hibernate.CacheMode;
import org.hibernate.Criteria;
import org.hibernate.EntityMode;
import org.hibernate.Filter;
import org.hibernate.FlushMode;
import org.hibernate.HibernateException;
import org.hibernate.LobHelper;
import org.hibernate.LockMode;
import org.hibernate.LockOptions;
import org.hibernate.Query;
import org.hibernate.ReplicationMode;
import org.hibernate.SQLQuery;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.Transaction;
import org.hibernate.TypeHelper;
import org.hibernate.UnknownProfileException;
import org.hibernate.jdbc.Work;
import org.hibernate.stat.SessionStatistics;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.ImmutableSet;
import com.google.inject.BindingAnnotation;
import com.google.inject.ConfigurationException;
import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.Key;
import com.google.inject.TypeLiteral;
import com.google.inject.spi.BindingTargetVisitor;
import com.google.inject.spi.ConstructorBinding;
import com.google.inject.spi.ConvertedConstantBinding;
import com.google.inject.spi.ExposedBinding;
import com.google.inject.spi.InjectionPoint;
import com.google.inject.spi.InstanceBinding;
import com.google.inject.spi.LinkedKeyBinding;
import com.google.inject.spi.Message;
import com.google.inject.spi.ProviderBinding;
import com.google.inject.spi.ProviderInstanceBinding;
import com.google.inject.spi.ProviderKeyBinding;
import com.google.inject.spi.UntargettedBinding;
import com.kaching.platform.bus.Handler;
import com.kaching.platform.bus.impl.Handlers;
import com.kaching.platform.common.Errors;
import com.kaching.platform.common.logging.Log;
import com.kaching.platform.guice.TypeLiterals;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.DbSessionImpl;
import com.kaching.platform.hibernate.WithReadOnlySession;
import com.kaching.platform.hibernate.WithReadOnlySessionExpression;
import com.kaching.platform.hibernate.WithSession;
import com.kaching.platform.hibernate.WithSessionExpression;
import com.kaching.platform.hibernate.WithStatelessSessionExpression;
import com.kaching.platform.monitoring.icinga.IcingaCheck;
import com.kaching.platform.queryengine.postprocessors.HttpRequestHeaders;
import com.kaching.platform.queryengine.postprocessors.HttpResponseHeaders;
import com.kaching.platform.queryengine.postprocessors.QueryOutput;
import com.twolattes.json.Entity;

public class InjectorTester {

  private static final Log log = getLog(InjectorTester.class);

  public static final String[] TEST_MODULE_ARGS = new String[]{
      "--host", "localhost", "--fake-mq", "--fake-jcollectd", "--disable-varz", "--fake-redis",
      "--fake-voldemort", "--fake-master", "--fake-nls", "--disable-zk", "--stub-file-system",
      "--fake-memcached", "--fake-sirius-client", "--fake-tinykv"
  };

  public static final String[] TEST_AURORA_MODULE_ARGS = new String[]{
      "--host", "localhost", "--fake-mq", "--fake-jcollectd", "--disable-varz", "--fake-redis",
      "--fake-voldemort", "--fake-master", "--fake-nls", "--fake-sirius-client", "--fake-tinykv",
      "--disable-zk", "--stub-file-system", "--fake-memcached", "-aurora-db", "*********************************"
  };

  private static final Set<Class<?>> TRANSACTION_CLASSES = ImmutableSet.of(
      WithReadOnlySession.class,
      WithReadOnlySessionExpression.class,
      WithSession.class,
      WithSessionExpression.class,
      WithStatelessSessionExpression.class);

  @Inject QueryProxies proxies;
  @Inject Injector injector;
  @Inject QueryScope queryScope;
  @Inject(optional = true) Handlers handlers;
  @Inject(optional = true) Set<Class<? extends IcingaCheck>> icingaCheckClasses;

  public static String[] mergeTestArgs(String... array) {
    List<String> buffer = new ArrayList<>();
    for (String x : TEST_MODULE_ARGS) {
      buffer.add(x);
    }
    for (String x : array) {
      if (!buffer.contains(x)) {
        buffer.add(x);
      }
    }
    return buffer.toArray(new String[buffer.size()]);
  }

  /**
   * Defines the mode in which to run the injection verification.
   */
  public enum Mode {

    /**
     * Extensive verification including some run-time checks.
     */
    EXTENSIVE {
      @Override
      public Class<?> verify(Injector injector, Key<?> key) {
        Object verifyByGettingInstance = injector.getInstance(key);
        return verifyByGettingInstance.getClass();
      }
    },

    /**
     * Verifies bindings only.
     */
    BINDINGS {
      @Override
      public Class<?> verify(Injector injector, Key<?> key) {
        checkNotNull(injector.getBinding(key));
        return injector.getBinding(key).acceptTargetVisitor(new BindingTargetVisitor<Object, Class<?>>() {
          Class<?> noSpecialHandling = key.getTypeLiteral().getRawType();

          @Override
          public Class<?> visit(InstanceBinding<?> binding) {
            return binding.getInstance().getClass();
          }

          @Override
          public Class<?> visit(ProviderInstanceBinding<?> binding) {
            return noSpecialHandling;
          }

          @Override
          public Class<?> visit(ProviderKeyBinding<?> binding) {
            /* could potentially paw through binding.getProviderKey().getTypeLiteral().getGenericInterfaces()
             * to look for Provider<Something> and return Something.class  */
            return noSpecialHandling;
          }

          @Override
          public Class<?> visit(LinkedKeyBinding<?> binding) {
            return binding.getLinkedKey().getTypeLiteral().getRawType();
          }

          @Override
          public Class<?> visit(ExposedBinding<?> binding) {
            /* could potentially paw through all the binding.getPrivateElements().getElements() looking for
             * the one that works with this Key, but I don't think we ever use PrivateModule anyway.  */
            return noSpecialHandling;
          }

          @Override
          public Class<?> visit(UntargettedBinding<?> binding) {
            throw new UnsupportedOperationException(
                "if you find a way to get this branch to happen, please make a test case for it!");
          }

          @Override
          public Class<?> visit(ConstructorBinding<?> binding) {
            return binding.getConstructor().getDeclaringType().getRawType();
          }

          @Override
          public Class<?> visit(ConvertedConstantBinding<?> binding) {
            return noSpecialHandling;
          }

          @Override
          public Class<?> visit(ProviderBinding<?> binding) {
            return binding.getProvidedKey().getTypeLiteral().getRawType();
          }
        });
      }
    };

    abstract Class<?> verify(Injector injector, Key<?> key);

  }

  /**
   * Runs all the tests and throw if errors are encountered.
   */
  public void runTests() {
    runTests(EXTENSIVE);
  }

  /**
   * Runs all the tests in context of enclosing class, and throw if errors are encountered
   */
  public void runTests(Class<?> enclosingClass) {
    runTestsAndProduceErrors(EXTENSIVE, enclosingClass).throwIfHasErrors();
  }

  /**
   * Runs all the tests and throw if errors are encountered.
   */
  public void runTests(Mode mode) {
    runTestsAndProduceErrors(mode).throwIfHasErrors();
  }

  /**
   * Runs all the tests and throw if errors are encountered.
   */
  public void runTests(Mode mode, Class<?> enclosingClass) {
    runTestsAndProduceErrors(mode, enclosingClass).throwIfHasErrors();
  }

  /**
   * Runs all the tests in context of enclosing class, and throw if errors
   */
  public Errors runTestsAndProduceErrors(Mode mode, Class<?> enclosingClass) {
    return runTestsAndProduceErrors(mode, Collections.singletonList(enclosingClass), Collections.emptyList());
  }

  /**
   * Runs all the tests and returns aggregated errors.
   */
  public Errors runTestsAndProduceErrors(Mode mode) {
    return runTestsAndProduceErrors(mode, Collections.emptyList(), Collections.emptyList());
  }

  /**
   * Runs all the tests and returns aggregated errors.
   */
  public Errors runTestsAndProduceErrors(
      Mode mode,
      Iterable<Class<?>> supplemental) {
    return runTestsAndProduceErrors(mode, supplemental, Collections.emptyList());
  }

  /**
   * Runs all the tests and returns aggregated errors.
   */
  public Errors runTestsAndProduceErrors(
      Mode mode,
      Iterable<Class<?>> supplemental,
      Iterable<Class<? extends com.kaching.platform.queryengine.Query<?>>> supplementalQueries) {
    Errors errors = new Errors();
    for (Entry<Class<?>, Throwable> entry : runTestsInternal(mode, supplemental, supplementalQueries).entrySet()) {
      if (entry.getValue() instanceof ConfigurationException) {
        ConfigurationException cfgEx = (ConfigurationException) entry.getValue();
        for (Message msg : cfgEx.getErrorMessages()) {
          errors.addMessage(msg.getMessage());
        }
      } else {
        errors.addMessage("%s: %s", entry.getKey(), entry.getValue());
      }
    }
    return errors;
  }

  @VisibleForTesting
  Map<Class<?>, Throwable> runTestsInternal(
      Mode mode,
      Iterable<Class<?>> supplemental,
      Iterable<Class<? extends com.kaching.platform.queryengine.Query<?>>> supplementalQueries) {
    HashSet<Class<? extends com.kaching.platform.queryengine.Query<?>>> supplementalQueriesToCheck =
        newHashSet(supplementalQueries);
    Map<Class<?>, Throwable> failures = newHashMap();
    for (QueryProxy<?> proxy : proxies) {
      addThrowable(failures, proxy, verifyQuery(mode, proxy));
      addThrowable(failures, proxy, canFindProcess(proxy));
      supplementalQueriesToCheck.remove(proxy.getQueryClass());
    }
    for (Class<? extends com.kaching.platform.queryengine.Query<?>> queryClass : supplementalQueriesToCheck) {
      QueryProxy<?> proxy = proxies.getProxy(queryClass);
      addThrowable(failures, proxy, verifyQuery(mode, proxy));
      addThrowable(failures, proxy, canFindProcess(proxy));
    }
    if (handlers != null) {
      queryScope.begin();
      for (Class<? extends Handler<?>> handler : handlers.getAllHandlers()) {
        addThrowable(failures, handler, verifyClassInjectable(mode, handler));
      }
      queryScope.end();
    }
    if (icingaCheckClasses != null) {
      for (Class<? extends IcingaCheck> icingaCheck : icingaCheckClasses) {
        queryScope.begin();
        addThrowable(failures, icingaCheck, verifyClassInjectable(mode, icingaCheck));
        queryScope.end();
      }
    }
    for (Class<?> supp : supplemental) {
      addThrowable(failures, supp, verifyClassInjectable(mode, supp));
    }
    for (Entry<Class<?>, Throwable> e : failures.entrySet()) {
      log.error(
          "Failed to inject %s: %s",
          e.getKey().getSimpleName(),
          e.getValue());
    }
    if (failures.size() > 0) {
      log.error("Failed to inject %s queries", failures.size());
    }
    return failures;
  }

  private void addThrowable(
      Map<Class<?>, Throwable> failures, QueryProxy<?> proxy,
      Throwable throwable) {
    addThrowable(failures, proxy.getQueryClass(), throwable);
  }

  private void addThrowable(
      Map<Class<?>, Throwable> failures, Class<?> clazz,
      Throwable throwable) {
    if (throwable != null) {
      failures.put(clazz, throwable);
    }
  }

  Throwable verifyQuery(Mode mode, QueryProxy<?> proxy) {
    Throwable throwable = verifyQuery(mode, proxy.getQueryClass(), false);
    if (throwable == null) {
      throwable = isPostProcessorInjectable(mode, proxy);
    }
    return throwable;
  }

  <T> Throwable canFindProcess(QueryProxy<?> proxy) {
    @SuppressWarnings("unchecked")
    Class<Query> queryClass = (Class<Query>) proxy.getQueryClass();
    Set<InjectionPoint> injectorPoints = InjectionPoint.forInstanceMethodsAndFields(queryClass);
    for (InjectionPoint injectorPoint : injectorPoints) {
      Member member = injectorPoint.getMember();
      if (!(member instanceof Field)) {
        return new IllegalStateException(format("member %s of class %s may not be injected", member, queryClass));
      }
    }
    return null;
  }

  public Throwable verifyQuery(Mode mode, Class<?> clazz, boolean impliedTransactional) {
    queryScope.begin();

    if (clazz.getAnnotation(Transactional.class) != null || impliedTransactional) {
      queryScope.cache(SESSION_KEY, new ThrowingSession());
      boolean isReadOnly = clazz.getAnnotation(Transactional.class) != null &&
                           clazz.getAnnotation(Transactional.class).readOnly();
      DbSession.Kind dbSessionKind = isReadOnly ? DbSession.Kind.READ_ONLY : DbSession.Kind.READ_WRITE;
      queryScope.cache(DB_SESSION_KEY, new DbSessionImpl(new ThrowingSession(), dbSessionKind));
    }

    queryScope.cache(Key.get(new TypeLiteral<Map<String, String>>() {}, HttpRequestHeaders.class),
        Collections.emptyMap());

    try {
      Throwable throwable = verifyAtInjectFields(mode, clazz);
      if (throwable != null) {
        return throwable;
      }
      throwable = verifyInjectedTransactions(mode, clazz);
      if (throwable != null) {
        return throwable;
      }
      return null;
    } finally {
      queryScope.end();
    }
  }

  private Throwable verifyAtInjectFields(Mode mode, Class<?> clazz) {
    try {
      for (Field field : getAllAccessibleFields(clazz)) {
        boolean check = false;
        Inject inject = field.getAnnotation(Inject.class);
        if (inject != null && !inject.optional()) {
          check = true;
        }
        if (!check) {
          javax.inject.Inject inject2 = field.getAnnotation(javax.inject.Inject.class);
          if (inject2 != null) {
            check = true;
          }
        }
        if (check) {
          Type genericType = field.getGenericType();
          Annotation annotation = extractFirstBindingAnnotation(field);
          Key<?> key;
          if (genericType instanceof ParameterizedType) {
            TypeLiteral<?> typeLiteral = TypeLiterals.get(
                field.getType(),
                ((ParameterizedType) genericType).getActualTypeArguments());
            key = (annotation != null) ?
                Key.get(typeLiteral, annotation) :
                Key.get(typeLiteral);
          } else {
            key = (annotation != null) ?
                Key.get(field.getType(), annotation) :
                Key.get(field.getType());
          }
          Throwable throwable = verifyClassInjectable(mode, key);
          if (throwable != null) {
            return throwable;
          }
        }
      }
    } catch (RuntimeException e) {
      StringWriter writer = new StringWriter();
      e.printStackTrace(new PrintWriter(writer));
      if (writer.toString().contains("java.sql.SQLException:")) {
        // ugly, to avoid the exception thrown by the fact that the
        // ManualIdGenerator constructor tries to connect to the DB
        return null;
      } else {
        return e;
      }
    }
    return null;
  }

  private Throwable verifyClassInjectable(Mode mode, Class<?> clazz) {
    return verifyClassInjectable(mode, Key.get(clazz));
  }

  private Throwable verifyClassInjectable(Mode mode, Key<?> key) {
    Class<?> requestedClass = key.getTypeLiteral().getRawType();
    Class<?> bestGuessActualInjectedClass;
    try {
      bestGuessActualInjectedClass = mode.verify(injector, key);
    } catch (RuntimeException e) {
      return e;
    }
    Throwable throwable = verifyInjectedTransactions(mode, requestedClass);
    if (throwable != null || bestGuessActualInjectedClass.equals(requestedClass)) {
      return throwable;
    }
    return verifyInjectedTransactions(mode, bestGuessActualInjectedClass);
  }

  private Throwable verifyInjectedTransactions(Mode mode, Class<?> clazz) {
    int i = 1;
    do {
      try {
        // The Java reflection API doesn't support retrieving anonymous classes, so we rely on the compiler's
        // Class$1, Class$2, ... naming convention and iterate until a ClassNotFoundException is thrown.
        Class<?> anonymousClass = Class.forName(clazz.getName() + "$" + i);
        boolean isTransaction = TRANSACTION_CLASSES.stream().anyMatch(c -> c.isAssignableFrom(anonymousClass));
        if (isTransaction) {
          boolean started = queryScope.isStarted();
          if (!started) {
            queryScope.begin();
          }
          Session parent = queryScope.get(SESSION_KEY);
          DbSession dbParent = queryScope.get(DB_SESSION_KEY);
          DbSession.Kind dbKindParent = queryScope.get(DB_SESSION_KIND_KEY);
          try {
            queryScope.cache(SESSION_KEY, new ThrowingSession());
            queryScope.cache(DB_SESSION_KEY, new DbSessionImpl(new ThrowingSession(), DbSession.Kind.READ_WRITE));
            queryScope.cache(DB_SESSION_KIND_KEY, DbSession.Kind.READ_WRITE);

            // We must use verifyAtInjectFields instead of verifyClassInjectable as Guice is unable to inject (or verify)
            // non-static inner classes. We get the same recursive behavior, but might miss some uncommon injection
            // points such as methods annotated with @Inject.
            Throwable throwable = verifyAtInjectFields(mode, anonymousClass);
            if (throwable != null) {
              return throwable;
            }
          } finally {
            queryScope.remove(SESSION_KEY);
            if (parent != null) {
              queryScope.cache(SESSION_KEY, parent);
            }
            queryScope.remove(DB_SESSION_KEY);
            if (dbParent != null) {
              queryScope.cache(DB_SESSION_KEY, dbParent);
            }
            queryScope.remove(DB_SESSION_KIND_KEY);
            if (dbParent != null) {
              queryScope.cache(DB_SESSION_KIND_KEY, dbKindParent);
            }
            if (!started) {
              queryScope.end();
            }
          }
        }
      } catch (ClassNotFoundException e) {
        break;
      }
      i++;
    } while (true);
    return null;
  }

  private List<Field> getAllAccessibleFields(Class<?> clazz) {
    try {
      List<Field> result = newArrayList(clazz.getDeclaredFields());
      Class<?> superclass = clazz.getSuperclass();
      if (superclass != null) {
        result.addAll(getAllAccessibleFields(superclass));
      }
      return result;
    } catch (Throwable e) {
      throw new RuntimeException(format("for class %s", clazz), e);
    }
  }

  private Annotation extractFirstBindingAnnotation(Field field) {
    List<Annotation> annotations = newArrayList(field.getAnnotations());
    for (Annotation annotation : newArrayList(annotations)) {
      if (annotation.annotationType().equals(Inject.class)) {
        annotations.remove(annotation);
      }
      if (annotation.annotationType().getAnnotation(BindingAnnotation.class) == null) {
        annotations.remove(annotation);
      }
    }
    return annotations.size() > 0 ? annotations.get(0) : null;
  }

  private Throwable isPostProcessorInjectable(Mode mode, QueryProxy<?> proxy) {
    queryScope.begin();
    queryScope.cache(Key.get(OutputStream.class, QueryOutput.class),
        new NullOutputStream());
    queryScope.cache(Key.get(new TypeLiteral<Map<String, String>>() {}, HttpResponseHeaders.class),
        Collections.emptyMap());
    try {
      Class<?> rawType = proxy.getReturnType().getRawType();
      try {
        injector.getInstance(SerializerFactory.class).getOrThrow(proxy.getReturnType());
        Constructor<?> constructor = proxy.getConstructor();
        for (Class<?> paramType : constructor.getParameterTypes()) {
          checkEntityType(paramType);
        }
        checkEntityType(rawType);
      } catch (RuntimeException e) {
        return e;
      }
      try {
        if (rawType.getAnnotation(Entity.class) != null) {
          injector.getInstance(SerializerFactory.class).getOrThrow(proxy.getReturnType());
        }
      } catch (RuntimeException e) {
        return e;
      }
    } finally {
      queryScope.end();
    }
    return null;
  }

  private void checkEntityType(Class<?> rawType) {
    if (rawType.getAnnotation(Entity.class) != null) {
      Class<?> superclass = rawType.getSuperclass();
      if (superclass != null && superclass.getCanonicalName().equals("java.lang.Record")) {
        return;
      }
      try {
        rawType.getDeclaredConstructor();
      } catch (NoSuchMethodException e) {
        throw new RuntimeException(format("no default constructor for type %s", rawType), e);
      }
    }
  }

  static class ThrowingSession implements Session {

    private static final long serialVersionUID = 1L;

    @Override
    public Transaction beginTransaction() throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public LockRequest buildLockRequest(LockOptions arg0) {
      throw new UnsupportedOperationException();
    }

    @Override
    public void cancelQuery() throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public void clear() {
      throw new UnsupportedOperationException();
    }

    @Override
    public Connection close() throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public Connection connection() throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public boolean contains(Object arg0) {
      throw new UnsupportedOperationException();
    }

    @Override
    public Criteria createCriteria(Class arg0) {
      throw new UnsupportedOperationException();
    }

    @Override
    public Criteria createCriteria(String arg0) {
      throw new UnsupportedOperationException();
    }

    @Override
    public Criteria createCriteria(Class arg0, String arg1) {
      throw new UnsupportedOperationException();
    }

    @Override
    public Criteria createCriteria(String arg0, String arg1) {
      throw new UnsupportedOperationException();
    }

    @Override
    public Query createFilter(Object arg0, String arg1) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public Query createQuery(String arg0) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public SQLQuery createSQLQuery(String arg0) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public void delete(Object arg0) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public void delete(String arg0, Object arg1) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public void disableFetchProfile(String arg0) throws UnknownProfileException {
      throw new UnsupportedOperationException();
    }

    @Override
    public void disableFilter(String arg0) {
      throw new UnsupportedOperationException();
    }

    @Override
    public Connection disconnect() throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public void doWork(Work arg0) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public void enableFetchProfile(String arg0) throws UnknownProfileException {
      throw new UnsupportedOperationException();
    }

    @Override
    public Filter enableFilter(String arg0) {
      throw new UnsupportedOperationException();
    }

    @Override
    public void evict(Object arg0) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public void flush() throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public Object get(Class arg0, Serializable arg1) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public Object get(String arg0, Serializable arg1) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public Object get(Class arg0, Serializable arg1, LockMode arg2) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public Object get(Class arg0, Serializable arg1, LockOptions arg2) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public Object get(String arg0, Serializable arg1, LockMode arg2) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public Object get(String arg0, Serializable arg1, LockOptions arg2) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public CacheMode getCacheMode() {
      throw new UnsupportedOperationException();
    }

    @Override
    public LockMode getCurrentLockMode(Object arg0) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public Filter getEnabledFilter(String arg0) {
      throw new UnsupportedOperationException();
    }

    @Override
    public EntityMode getEntityMode() {
      throw new UnsupportedOperationException();
    }

    @Override
    public String getEntityName(Object arg0) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public FlushMode getFlushMode() {
      throw new UnsupportedOperationException();
    }

    @Override
    public Serializable getIdentifier(Object arg0) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public LobHelper getLobHelper() {
      throw new UnsupportedOperationException();
    }

    @Override
    public Query getNamedQuery(String arg0) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public Session getSession(EntityMode arg0) {
      throw new UnsupportedOperationException();
    }

    @Override
    public SessionFactory getSessionFactory() {
      throw new UnsupportedOperationException();
    }

    @Override
    public SessionStatistics getStatistics() {
      throw new UnsupportedOperationException();
    }

    @Override
    public Transaction getTransaction() {
      throw new UnsupportedOperationException();
    }

    @Override
    public TypeHelper getTypeHelper() {
      throw new UnsupportedOperationException();
    }

    @Override
    public boolean isConnected() {
      throw new UnsupportedOperationException();
    }

    @Override
    public boolean isDefaultReadOnly() {
      throw new UnsupportedOperationException();
    }

    @Override
    public boolean isDirty() throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public boolean isFetchProfileEnabled(String arg0) throws UnknownProfileException {
      throw new UnsupportedOperationException();
    }

    @Override
    public boolean isOpen() {
      throw new UnsupportedOperationException();
    }

    @Override
    public boolean isReadOnly(Object arg0) {
      throw new UnsupportedOperationException();
    }

    @Override
    public Object load(Class arg0, Serializable arg1) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public Object load(String arg0, Serializable arg1) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public void load(Object arg0, Serializable arg1) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public Object load(Class arg0, Serializable arg1, LockMode arg2) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public Object load(Class arg0, Serializable arg1, LockOptions arg2) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public Object load(String arg0, Serializable arg1, LockMode arg2) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public Object load(String arg0, Serializable arg1, LockOptions arg2) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public void lock(Object arg0, LockMode arg1) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public void lock(String arg0, Object arg1, LockMode arg2) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public Object merge(Object arg0) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public Object merge(String arg0, Object arg1) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public void persist(Object arg0) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public void persist(String arg0, Object arg1) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public void reconnect() throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public void reconnect(Connection arg0) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public void refresh(Object arg0) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public void refresh(Object arg0, LockMode arg1) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public void refresh(Object arg0, LockOptions arg1) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public void replicate(Object arg0, ReplicationMode arg1) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public void replicate(String arg0, Object arg1, ReplicationMode arg2) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public Serializable save(Object arg0) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public Serializable save(String arg0, Object arg1) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public void saveOrUpdate(Object arg0) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public void saveOrUpdate(String arg0, Object arg1) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public void setCacheMode(CacheMode arg0) {
      throw new UnsupportedOperationException();
    }

    @Override
    public void setDefaultReadOnly(boolean arg0) {
      throw new UnsupportedOperationException();
    }

    @Override
    public void setFlushMode(FlushMode arg0) {
      throw new UnsupportedOperationException();
    }

    @Override
    public void setReadOnly(Object arg0, boolean arg1) {
      throw new UnsupportedOperationException();
    }

    @Override
    public void update(Object arg0) throws HibernateException {
      throw new UnsupportedOperationException();
    }

    @Override
    public void update(String arg0, Object arg1) throws HibernateException {
      throw new UnsupportedOperationException();
    }

  }

}
