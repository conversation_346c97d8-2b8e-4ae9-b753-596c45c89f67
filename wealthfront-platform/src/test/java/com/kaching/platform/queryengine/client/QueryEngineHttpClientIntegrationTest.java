package com.kaching.platform.queryengine.client;

import static org.hamcrest.Matchers.instanceOf;
import static org.hamcrest.Matchers.is;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.fail;

import java.net.SocketTimeoutException;
import java.net.URI;
import java.util.concurrent.TimeUnit;

import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.localserver.LocalServerTestBase;
import org.joda.time.Duration;
import org.junit.Before;
import org.junit.Test;
import org.perf4j.StopWatch;

import com.google.common.collect.Lists;
import com.google.inject.Guice;
import com.kaching.DefaultJsonMarshallerFactory;
import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.QueryRuntimeMonitor;
import com.kaching.platform.queryengine.SerializerFactory;
import com.kaching.platform.queryengine.TwoLattesSerializerProvider;
import com.kaching.util.DefaultSleeper;
import com.kaching.util.Sleeper;
import com.kaching.util.http.ApacheHttpPoolingClient;
import com.kaching.util.http.HttpClient;
import com.kaching.util.http.QP0P1Formatter;

public class QueryEngineHttpClientIntegrationTest extends LocalServerTestBase {

  private final Sleeper sleeper = new DefaultSleeper();
  private final HttpClient apacheHttpClient =
      new ApacheHttpPoolingClient(StopWatch::new, 800, 700, Guice.createInjector()) {
        @Override
        protected int getMaxTotalConnections() {
          return 5;
        }

        @Override
        protected int getDefaultMaxConnectionsPerHost() {
          return 5;
        }

        @Override
        protected int getConnectionManagerTimeoutMillis() {
          return 200;
        }
      };

  @Before
  public void before() {
    serverBootstrap.registerHandler("/slow/*", (request, response, context) -> {
      String uri = request.getRequestLine().getUri();
      int slash = uri.lastIndexOf('/');
      Duration sleepTime = slash < uri.length() - 1 ?
          Duration.millis(Integer.parseInt(uri.substring(slash + 1)))
          : Duration.standardSeconds(1);
      response.setStatusCode(200);
      response.setEntity(new ByteArrayEntity(new FakeIntegerQuery(5).process().toString().getBytes()));

      sleeper.sleep(sleepTime);
    });
  }

  @Test
  public void success() throws Exception {
    start();
    URI uri = URI.create("http://localhost:" + server.getLocalPort() + "/echo/");

    QueryEngineHttpClient client = createQueryEngineHttpClient();
    byte[] result = client.invoke(uri, "query", Lists.newArrayList("hello", "world"));
    assertEquals("q=query&p0=hello&p1=world", new String(result));

    server.shutdown(1L, TimeUnit.SECONDS);
  }

  @Test
  public void socketTimeout() throws Exception {
    start();
    URI uri = URI.create("http://localhost:" + server.getLocalPort() + "/slow/5000");

    QueryEngineHttpClient client = createQueryEngineHttpClient();
    try {
      client.invoke(uri, "query", Lists.newArrayList("hello", "world"));
      fail();
    } catch (RuntimeException e) {
      assertThat(e.getCause(), is(instanceOf(SocketTimeoutException.class)));
    }

    server.shutdown(1L, TimeUnit.SECONDS);
  }

  private QueryEngineHttpClient createQueryEngineHttpClient() {
    SerializerFactory serializerFactory = new SerializerFactory(
        new TwoLattesSerializerProvider(new DefaultJsonMarshallerFactory()));
    return new QueryEngineHttpClient(() -> getBaseHttpClient().getHttpClient().get(), new QP0P1Formatter(),
        serializerFactory);
  }

  private BaseHttpClient<KachingServices.FAKE> getBaseHttpClient() {
    return new BaseHttpClient<>(
        KachingServices.FAKE.class,
        null,
        new QP0P1Formatter(),
        () -> apacheHttpClient,
        null, StopWatch::new,
        null,
        null,
        null,
        new QueryRuntimeMonitor()
    );
  }

  static class FakeIntegerQuery extends AbstractQuery<Integer> {

    private final int param;

    FakeIntegerQuery(int param) {
      this.param = param;
    }

    @Override
    public Integer process() {
      return param;
    }

  }

}
