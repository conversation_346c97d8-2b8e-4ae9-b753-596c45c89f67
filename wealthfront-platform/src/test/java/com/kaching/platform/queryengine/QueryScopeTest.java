package com.kaching.platform.queryengine;

import static com.google.inject.name.Names.named;
import static com.kaching.platform.queryengine.QueryScope.ScopeRules.RELAXED;
import static com.kaching.platform.queryengine.QueryScope.ScopeRules.STRICT;
import static com.wealthfront.test.Assert.assertNotEquals;
import static com.wealthfront.test.Assert.assertThrows;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import java.util.Collections;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import org.junit.Test;

import com.google.common.collect.ImmutableMap;
import com.google.inject.AbstractModule;
import com.google.inject.Guice;
import com.google.inject.Inject;
import com.google.inject.Injector;
import com.google.inject.Key;
import com.google.inject.OutOfScopeException;
import com.google.inject.Provider;
import com.google.inject.name.Named;
import com.kaching.platform.guice.CloseableScope;
import com.kaching.platform.guice.Scoper;

public class QueryScopeTest {

  @Test
  public void testInjectResetSingleThread() {
    final int[] i = new int[]{0};
    final QueryScope queryScope = new QueryScope(STRICT);

    // injector
    Injector injector = Guice.createInjector(new AbstractModule() {
      @Override
      protected void configure() {
        bind(String.class).toProvider(new Provider<String>() {
          public String get() {
            return String.valueOf(i[0]++);
          }
        }).in(queryScope);
      }
    });

    // testing
    queryScope.begin();
    assertEquals("0", injector.getInstance(String.class));
    assertEquals("0", injector.getInstance(String.class));
    queryScope.end();
    queryScope.begin();
    assertEquals("1", injector.getInstance(String.class));
    assertEquals("1", injector.getInstance(String.class));
    queryScope.end();

  }

  @Test
  public void testInjectResetTwoThreads() throws InterruptedException {
    final int[] i = new int[]{0};
    final QueryScope queryScope = new QueryScope(STRICT);

    // injector
    final Injector injector = Guice.createInjector(new AbstractModule() {
      @Override
      protected void configure() {
        bind(String.class).toProvider(new Provider<String>() {
          public String get() {
            return String.valueOf(i[0]++);
          }
        }).in(queryScope);
      }
    });

    // testing
    final String[] s1 = new String[4];
    final String[] s2 = new String[4];

    Thread t2 = new Thread() {
      @Override
      public void run() {
        queryScope.begin();
        s1[0] = injector.getInstance(String.class);
        s1[1] = injector.getInstance(String.class);
        queryScope.end();
        queryScope.begin();
        s1[2] = injector.getInstance(String.class);
        s1[3] = injector.getInstance(String.class);
        queryScope.end();
      }
    };
    t2.start();
    Thread t1 = new Thread() {
      @Override
      public void run() {
        queryScope.begin();
        s2[0] = injector.getInstance(String.class);
        s2[1] = injector.getInstance(String.class);
        queryScope.end();
        queryScope.begin();
        s2[2] = injector.getInstance(String.class);
        s2[3] = injector.getInstance(String.class);
        queryScope.end();
      }
    };
    t1.start();

    t1.join();
    t2.join();

    // pairs must be equal
    assertEquals(s1[0], s1[1]);
    assertEquals(s1[2], s1[3]);
    assertEquals(s2[0], s2[1]);
    assertEquals(s2[2], s2[3]);

    // non pairs must be different
    assertNotEquals(s1[0], s1[2]);
    assertNotEquals(s2[0], s2[2]);
  }

  @Test
  public void testInjectAnnotatedInstances() throws Exception {
    final int[] i = new int[]{0};
    final QueryScope queryScope = new QueryScope(STRICT);

    // injector
    Injector injector = Guice.createInjector(new AbstractModule() {
      @Override
      protected void configure() {
        bind(String.class).annotatedWith(named("a")).toProvider(new Provider<String>() {
          public String get() {
            return String.valueOf(i[0]++);
          }
        }).in(queryScope);
        bind(String.class).annotatedWith(named("b")).toProvider(new Provider<String>() {
          public String get() {
            return String.valueOf(i[0]++);
          }
        }).in(queryScope);
      }
    });

    // testing
    Container c = new Container();
    queryScope.begin();
    injector.injectMembers(c);
    queryScope.end();

    assertEquals("0", c.a);
    assertEquals("1", c.b);
  }

  static class Container {

    @Inject @Named("a") String a;
    @Inject @Named("b") String b;
  }

  @Test
  public void twoInstanceOfQueryScopeDoNotShareState() throws Exception {
    QueryScope scope1 = new QueryScope(RELAXED);
    QueryScope scope2 = new QueryScope(RELAXED);

    scope1.cache(Key.get(String.class), "Hello");
    assertNull(scope2.get(Key.get(String.class)));
  }

  @Test(expected = UnsupportedOperationException.class)
  public void emptyScope() throws Exception {
    QueryScope scope = new QueryScope(RELAXED);
    Provider<String> provider =
        scope.scope(Key.get(String.class), new Provider<String>() {
          public String get() {
            throw new UnsupportedOperationException();
          }
        });
    scope.begin();
    provider.get();
    scope.end();
  }

  @Test
  public void scopingWithAString() throws Exception {
    QueryScope scope = new QueryScope(RELAXED);
    Provider<String> provider =
        scope.scope(Key.get(String.class), new Provider<String>() {
          public String get() {
            throw new UnsupportedOperationException();
          }
        });
    scope.begin(ImmutableMap.of(Key.get(String.class), "Hello"));
    assertEquals("Hello", provider.get());
    scope.end();
  }

  @Test
  public void scopingWithNothingThenAString() throws Exception {
    QueryScope scope = new QueryScope(RELAXED);
    Provider<String> provider =
        scope.scope(Key.get(String.class), new Provider<String>() {
          public String get() {
            return "Hello";
          }
        });
    assertEquals("Hello", provider.get());
  }

  @Test(expected = UnsupportedOperationException.class)
  public void scopingWithANullString() throws Exception {
    QueryScope scope = new QueryScope(RELAXED);
    Provider<String> provider =
        scope.scope(Key.get(String.class), new Provider<String>() {
          public String get() {
            throw new UnsupportedOperationException();
          }
        });
    scope.begin(Collections.singletonMap(Key.get(String.class), null));
    provider.get();
    scope.end();
  }

  @Test
  public void scopingWithANullStringThenAString() throws Exception {
    QueryScope scope = new QueryScope(RELAXED);
    Provider<String> provider =
        scope.scope(Key.get(String.class), new Provider<String>() {
          public String get() {
            return "Hello";
          }
        });
    scope.begin(Collections.singletonMap(Key.get(String.class), null));
    assertEquals("Hello", provider.get());
    scope.end();
  }

  @Test
  public void transfer_modificationsAfterScoperShouldNotChangeCache() throws Exception {
    final Key<String> key = Key.get(String.class);
    final ExecutorService executor = Executors.newSingleThreadExecutor();

    try {
      final QueryScope scope = new QueryScope(STRICT);
      scope.begin();
      scope.cache(key, "value1");
      final Scoper scoper = scope.transfer(true);
      scope.cache(key, "value2");
      assertEquals("value1", executor.submit(scoper.wrapCallable(() -> scope.get(key))).get());
      assertEquals("value2", scope.get(key));
    } finally {
      shutdownExecutor(executor);
    }
  }

  @Test
  public void transfer_notStarted_strict() throws Exception {
    final QueryScope scope = new QueryScope(STRICT);
    String expectedMessage = "Must call QueryScope.begin() before any scoped providers will work.";
    assertThrows(OutOfScopeException.class, expectedMessage, () -> scope.transfer(true));
    assertThrows(OutOfScopeException.class, expectedMessage, () -> scope.transfer());
    assertNotNull(scope.transfer(false));
  }

  @Test
  public void transfer_notStarted_relaxed() throws Exception {
    final QueryScope scope = new QueryScope(RELAXED);
    assertNotNull(scope.transfer(true));
    assertNotNull(scope.transfer());
  }

  @Test
  public void transfer_null_skipBegin() throws Exception {
    final QueryScope scope = new QueryScope(STRICT);
    Scoper scoper = scope.transfer(false);
    CompletableFuture.runAsync(() -> {
      try (CloseableScope closer = scoper.open()) {
        assertFalse(scope.isStarted());
      }
    }).join();
  }

  @Test
  public void transfer_shouldBegin() throws Exception {
    final Key<String> key = Key.get(String.class);
    final ExecutorService executor = Executors.newSingleThreadExecutor();

    try {
      final QueryScope scope = new QueryScope(STRICT);
      scope.begin();
      scope.cache(key, "value");
      Scoper scoper = scope.transfer();
      assertEquals("value", executor.submit(() -> {
        try (CloseableScope closer = scoper.open()) {
          assertTrue(scope.isStarted());
          return scope.get(key);
        }
      }).get());

      assertFalse(executor.submit(() -> scope.isStarted()).get());
    } finally {
      shutdownExecutor(executor);
    }
  }

  @Test
  public void transfer_shouldClose() throws Exception {
    final Key<String> key = Key.get(String.class);
    final ExecutorService executor = Executors.newSingleThreadExecutor();

    try {
      final QueryScope scope = new QueryScope(STRICT);
      scope.begin();
      scope.cache(key, "value");
      Scoper scoper = scope.transfer();
      assertEquals("value", executor.submit(() -> scoper.openAndSupply(() -> scope.get(key))).get());

      assertFalse(executor.submit(() -> scope.isStarted()).get());
    } finally {
      shutdownExecutor(executor);
    }
  }

  @Test
  public void transfer_shouldNotBegin() throws Exception {
    final Key<String> key = Key.get(String.class);
    final ExecutorService executor = Executors.newSingleThreadExecutor();

    try {
      final QueryScope scope = new QueryScope(STRICT);
      final Scoper scoper = scope.transfer(false);
      try {
        executor.submit(() -> scoper.openAndRun(() -> scope.get(key))).get();
        fail();
      } catch (ExecutionException e) {
        assertTrue(e.getCause() instanceof IllegalStateException);
        assertEquals("Must call QueryScope.begin() before any scoped providers will work.", e.getCause().getMessage());
      }
    } finally {
      shutdownExecutor(executor);
    }
  }

  @Test
  public void transfer_shouldResetToPrevious() throws Exception {
    final Key<String> key = Key.get(String.class);
    final ExecutorService executor = Executors.newSingleThreadExecutor();

    try {
      final QueryScope scope = new QueryScope(STRICT);
      executor.submit(() -> {
        scope.begin(ImmutableMap.of(key, "value-child"));
      }).get();

      scope.begin();
      scope.cache(key, "value");
      final Scoper scoper = scope.transfer();
      assertEquals("value", executor.submit(() -> scoper.openAndSupply(() -> scope.get(key))).get());

      assertEquals("value-child", executor.submit(() -> scope.get(key)).get());
    } finally {
      shutdownExecutor(executor);
    }
  }

  private void shutdownExecutor(ExecutorService executor) {
    executor.shutdown();
    try {
      executor.awaitTermination(10, TimeUnit.SECONDS);
    } catch (InterruptedException e) {
      executor.shutdownNow();
    }
  }

}
