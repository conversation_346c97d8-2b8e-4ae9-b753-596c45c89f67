package com.kaching.platform.queryengine;

import static com.kaching.platform.queryengine.FakeStackTraceMonitorTest.State.ILLEGAL;
import static com.wealthfront.test.Assert.assertThrows;
import static com.wealthfront.util.time.DateTimeZones.ET;
import static org.junit.Assert.assertTrue;

import org.joda.time.DateTime;
import org.junit.Before;
import org.junit.Test;

import com.kaching.platform.common.Strings;

public class FakeStackTraceMonitorTest {

  protected enum State {
    ILLEGAL
  }

  private final DateTime now = new DateTime(2024, 5, 30, 3, 30, 0, ET);
  private final FakeStackTraceMonitor fakeStackTraceMonitor = new FakeStackTraceMonitor();

  @Before
  public void before() {
    fakeStackTraceMonitor.add(new IllegalStateException(
        Strings.format("Some object was in an illegal state: %s at time: %s", ILLEGAL, now)
    ));
  }

  @Test
  public void getOnlyStackTraceAndClear_oneStackTrace_passesAssertion() {
    fakeStackTraceMonitor.getOnlyStackTraceAndClear();
  }

  @Test
  public void getOnlyStackTraceAndClear_multipleStackTraces_throwsException() {
    fakeStackTraceMonitor.add(
        new NullPointerException("Cannot invoke \"java.lang.Class.toString()\" because \"exceptionClass\" is null")
    );
    fakeStackTraceMonitor.add(
        new IllegalArgumentException("Argument provided was illegal")
    );

    try {
      fakeStackTraceMonitor.getOnlyStackTraceAndClear();
    } catch (IllegalArgumentException e) {
      assertTrue(e.getMessage().contains("expected one element but was:"));
      assertTrue(e.getMessage().contains("java.lang.IllegalStateException"));
      assertTrue(e.getMessage().contains("java.lang.NullPointerException"));
      assertTrue(e.getMessage().contains("java.lang.IllegalArgumentException"));
    }
  }

  @Test
  public void assertOnlyStackTraceAndClear_oneLongSubstring_passesAssertion() {
    fakeStackTraceMonitor.assertOnlyStackTraceAndClear(
        "java.lang.IllegalStateException: Some object was in an illegal state: ILLEGAL at time: 2024-05-30T03:30:00.000-04:00"
    );
  }

  @Test
  public void assertOnlyStackTraceAndClear_multipleSubstrings_passesAssertion() {
    fakeStackTraceMonitor.assertOnlyStackTraceAndClear(
        "IllegalStateException",
        "Some object was in an illegal state:",
        ILLEGAL.toString(),
        now.toString()
    );
  }

  @Test
  public void assertOnlyStackTraceAndClear_withExceptionClassParam_standardCase_passesAssertion() {
    fakeStackTraceMonitor.assertOnlyStackTraceAndClear(
        IllegalStateException.class,
        "Some object was in an illegal state:",
        ILLEGAL.toString(),
        now.toString()
    );
  }

  @Test
  public void assertOnlyStackTraceAndClear_withExceptionClassParam_differentExceptionClass_failsAssertion() {
    try {
      fakeStackTraceMonitor.assertOnlyStackTraceAndClear(
          Exception.class,
          "Some object was in an illegal state:",
          ILLEGAL.toString(),
          now.toString()
      );
    } catch (AssertionError e) {
      assertTrue(e.getMessage().contains(
          """
              Expected: a string containing "java.lang.Exception"
                   but: was "java.lang.IllegalStateException: Some object was in an illegal state: ILLEGAL at time: 2024-05-30T03:30:00.000-04:00"""
      ));
    }
  }

  @Test
  public void assertOnlyStackTraceAndClear_withExceptionClassParam_nullExceptionClass_throwsNpe() {
    assertThrows(
        NullPointerException.class,
        "Cannot invoke \"java.lang.Class.toString()\" because \"exceptionClass\" is null",
        () -> fakeStackTraceMonitor.assertOnlyStackTraceAndClear(
            (Class<? extends Exception>) null,
            "This substring is not in the added exception",
            ILLEGAL.toString(),
            now.toString()
        )
    );
  }

  @Test
  public void assertSubstringsAreInStackTrace_noSubstrings_passesAssertion() {
    fakeStackTraceMonitor.assertSubstringsAreInStackTrace(
        fakeStackTraceMonitor.getOnlyStackTraceAndClear()
    );
  }

  @Test
  public void assertSubstringsAreInStackTrace_withExceptionClassParam_overlappingSubstrings_passesAssertion() {
    fakeStackTraceMonitor.assertSubstringsAreInStackTrace(
        fakeStackTraceMonitor.getOnlyStackTraceAndClear(),
        "IllegalStateException",
        "Exception: Some object was in an illegal state:",
        "state: " + ILLEGAL,
        "at time: ",
        "time: " + now
    );
  }

  @Test
  public void assertSubstringsAreInStackTrace_substringThatDoesntExist_failsAssertion() {
    try {
      fakeStackTraceMonitor.assertSubstringsAreInStackTrace(
          fakeStackTraceMonitor.getOnlyStackTraceAndClear(),
          "This substring is not in the added exception",
          ILLEGAL.toString(),
          now.toString()
      );
    } catch (AssertionError e) {
      assertTrue(e.getMessage().contains(
          """
              Expected: a string containing "This substring is not in the added exception"
                   but: was "java.lang.IllegalStateException: Some object was in an illegal state: ILLEGAL at time: %s"""
              .formatted(now.toString())
      ));
    }
  }

  @Test
  public void assertSubstringsAreInStackTrace_emptySubstrings_failsAssertion() {
    try {
      fakeStackTraceMonitor.assertSubstringsAreInStackTrace(
          fakeStackTraceMonitor.getOnlyStackTraceAndClear(),
          ""
      );
    } catch (AssertionError e) {
      assertTrue(e.getMessage().contains(
          """
              Expected: is not an empty string
                   but: was "\""""
      ));
    }
  }

  @Test
  public void assertSubstringsAreInStackTrace_nullSubstrings_throwsNpe() {
    assertThrows(
        NullPointerException.class,
        "Cannot read the array length because \"<local3>\" is null",
        () -> fakeStackTraceMonitor.assertSubstringsAreInStackTrace(
            fakeStackTraceMonitor.getOnlyStackTraceAndClear(),
            null
        )
    );
  }

  @Test
  public void assertSubstringsAreInStackTrace_oneNullSubstrings_throwsNpe() {
    assertThrows(
        IllegalArgumentException.class,
        "missing substring",
        () -> fakeStackTraceMonitor.assertSubstringsAreInStackTrace(
            fakeStackTraceMonitor.getOnlyStackTraceAndClear(),
            "Some object",
            null
        )
    );
  }

}
