package com.kaching.platform.queryengine;

import static com.kaching.platform.queryengine.QueryScope.ScopeRules.STRICT;
import static com.kaching.platform.queryengine.QueryScopeFactory.queryScope;
import static com.wealthfront.test.Assert.assertOptionEquals;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import org.junit.Test;

import com.google.inject.Key;
import com.kaching.platform.common.Option;
import com.kaching.util.functional.Pointer;

public class QueryScopeFactoryTest {

  private static final Key<String> key = Key.get(String.class);
  private static final String value = "TEST";
  private final QueryScope queryScope = new QueryScope(STRICT);

  @Test
  public void queryScopeIsStarted() {
    QueryScope scope = queryScope(queryScope).build();
    assertTrue(scope.isStarted());
  }

  @Test
  public void cacheNonNullObject() {
    QueryScope scope = queryScope(queryScope).with(key, value).build();
    assertTrue(scope.isStarted());
    assertEquals(value, scope.get(key));
  }

  @Test
  public void ignoreNullObject() {
    String value = null;
    QueryScope scope = queryScope(queryScope).with(key, value).build();
    assertTrue(scope.isStarted());
    assertNull(scope.get(key));
  }

  @Test
  public void cacheDefinedOption() {
    Option<String> option = Option.some(value);
    QueryScope scope = queryScope(queryScope).with(key, option).build();
    assertTrue(scope.isStarted());
    assertOptionEquals(scope.get(key), option);
  }

  @Test
  public void ignoreEmptyOption() {
    Option<String> option = Option.none();
    QueryScope scope = queryScope(queryScope).with(key, option).build();
    assertTrue(scope.isStarted());
    assertNull(scope.get(key));
  }

  @Test
  public void cacheSetPointer() {
    Pointer<String> pointer = Pointer.pointer();
    pointer.set(value);
    QueryScope scope = queryScope(queryScope).with(key, pointer).build();
    assertTrue(scope.isStarted());
    assertNotNull(scope.get(key));
    assertEquals(value, scope.get(key));
  }

  @Test
  public void ignoreEmptyPointer() {
    Pointer<String> pointer = Pointer.pointer();
    QueryScope scope = queryScope(queryScope).with(key, pointer).build();
    assertTrue(scope.isStarted());
    assertNull(scope.get(key));
  }

}
