package com.kaching.platform.multicolo;

import static com.wealthfront.test.Assert.assertThrows;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import org.I0Itec.zkclient.IZkDataListener;
import org.I0Itec.zkclient.IZkStateListener;
import org.I0Itec.zkclient.ZkClient;
import org.hamcrest.Matchers;
import org.jmock.Expectations;
import org.junit.After;
import org.junit.Test;

import com.kaching.platform.queryengine.StackTraceMonitor;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.Mockeries.WFMockery;

public class IsMasterColoTestPredicateImplTest {
  
  private static final String ISMASTER_TEST_ZK_NODE = "/multicolo/current/ismastertest";

  private final WFMockery mockery = Mockeries.mockery(true);
  private final ZkClient zkClient = mockery.mock(ZkClient.class);
  private final StackTraceMonitor stackTraceMonitor = mockery.mock(StackTraceMonitor.class);

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void satisfiedReturnsValueAfterConstruction() {
    mockery.checking(new Expectations() {{
      oneOf(zkClient).subscribeDataChanges(with(equal(ISMASTER_TEST_ZK_NODE)),
          with(Matchers.<IZkDataListener>instanceOf(IZkDataListener.class)));
      oneOf(zkClient).subscribeStateChanges(with(Matchers.<IZkStateListener>instanceOf(IZkStateListener.class)));
      oneOf(zkClient).read(ISMASTER_TEST_ZK_NODE);
      will(returnValue("true".getBytes()));
    }});

    IsMasterColoTestPredicate predicate = new IsMasterColoTestPredicateImpl(zkClient, stackTraceMonitor);
    assertTrue(predicate.satisfied());
  }

  @Test
  public void constructorThrowsWithBadData() {
    mockery.checking(new Expectations() {{
      oneOf(zkClient).subscribeDataChanges(with(equal(ISMASTER_TEST_ZK_NODE)),
          with(Matchers.<IZkDataListener>instanceOf(IZkDataListener.class)));
      oneOf(zkClient).subscribeStateChanges(with(Matchers.<IZkStateListener>instanceOf(IZkStateListener.class)));
      oneOf(zkClient).read(ISMASTER_TEST_ZK_NODE);
      will(returnValue("hermione".getBytes()));
      oneOf(stackTraceMonitor).add(with(Matchers.<Throwable>instanceOf(RuntimeException.class)));
    }});

    assertThrows(RuntimeException.class, new Runnable() {
      @Override
      public void run() {
        new IsMasterColoTestPredicateImpl(zkClient, stackTraceMonitor);
      }
    });
  }

  @Test
  public void satisfiedIgnoresCase() {
    mockery.checking(new Expectations() {{
      exactly(4).of(zkClient).subscribeDataChanges(with(equal(ISMASTER_TEST_ZK_NODE)),
          with(Matchers.<IZkDataListener>instanceOf(IZkDataListener.class)));
      exactly(4).of(zkClient)
          .subscribeStateChanges(with(Matchers.<IZkStateListener>instanceOf(IZkStateListener.class)));
      exactly(4).of(zkClient).read(ISMASTER_TEST_ZK_NODE);
      will(onConsecutiveCalls(
          returnValue("false".getBytes()),
          returnValue("TRUE".getBytes()),
          returnValue("False".getBytes()),
          returnValue("tRuE".getBytes())
      ));
    }});

    assertFalse(new IsMasterColoTestPredicateImpl(zkClient, stackTraceMonitor).satisfied());
    assertTrue(new IsMasterColoTestPredicateImpl(zkClient, stackTraceMonitor).satisfied());
    assertFalse(new IsMasterColoTestPredicateImpl(zkClient, stackTraceMonitor).satisfied());
    assertTrue(new IsMasterColoTestPredicateImpl(zkClient, stackTraceMonitor).satisfied());
  }

}