package com.kaching.platform.hibernate.search;

import static com.kaching.DefaultKachingMarshallers.createEntityMarshaller;
import static com.wealthfront.test.Assert.assertMarshalling;
import static org.junit.Assert.assertEquals;

import org.junit.Test;

import com.twolattes.json.Json;
import com.twolattes.json.Marshaller;

public class EntitySearchCapabilityTest {

  private final Marshaller<EntitySearchCapability> marshaller = createEntityMarshaller(EntitySearchCapability.class);

  @Test
  public void marshalling() {
    EntitySearchCapability capability = new EntitySearchCapability("entityClassName", true, true, "database", "kind");
    Json.Value expected = Json.object(
        "entityClassName", "entityClassName",
        "hasUserIdMapping", true,
        "hasAccountIdMapping", true,
        "databaseName", "database",
        "serviceKindSimpleName", "kind"
    );
    assertMarshalling(marshaller, expected, capability);
  }

  @Test
  public void getters() {
    EntitySearchCapability capability = new EntitySearchCapability("entityClassName", true, true, "database", "kind");
    assertEquals("entityClassName", capability.getEntityClassName());
    assertEquals(true, capability.hasUserIdMapping());
    assertEquals(true, capability.hasAccountIdMapping());
    assertEquals("database", capability.getDatabaseName());
    assertEquals("kind", capability.getServiceKindSimpleName());
  }

}