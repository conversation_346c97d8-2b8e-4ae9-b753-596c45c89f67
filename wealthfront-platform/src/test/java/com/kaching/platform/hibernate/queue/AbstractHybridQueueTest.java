package com.kaching.platform.hibernate.queue;

import static com.kaching.platform.hibernate.queue.AbstractHybridQueueEntity.IGNORED_AT_DUMMY_VALUE;
import static com.kaching.platform.multicolo.MultiColoStatusProvider.ColoStatus.MASTER;
import static com.kaching.platform.multicolo.MultiColoStatusProvider.ColoStatus.MOCK_FAILOVER_FAKE_MASTER;
import static com.wealthfront.test.Assert.assertEmpty;
import static com.wealthfront.test.Assert.assertThrows;
import static com.wealthfront.util.stream.WFCollectors.pairsToMap;
import static com.wealthfront.util.time.DateTimeZones.ET;
import static java.util.Collections.emptyMap;
import static org.hamcrest.Matchers.containsString;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertThat;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import org.joda.time.DateTime;
import org.joda.time.Duration;
import org.junit.BeforeClass;
import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Iterables;
import com.google.inject.Provider;
import com.google.inject.util.Providers;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Pair;
import com.kaching.platform.discovery.ResolutionException;
import com.kaching.platform.functional.Unchecked;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.queue.AbstractHybridQueue.WrappedComparable;
import com.kaching.platform.hibernate.queue.AbstractHybridQueueExampleTest.TestHybridQueue;
import com.kaching.platform.queryengine.FakeStackTraceMonitor;
import com.kaching.platform.queryengine.exceptions.InvalidArgumentException;
import com.kaching.platform.queryengine.exceptions.NotFoundException;
import com.kaching.platform.zk.FakeLocalAnnouncement;
import com.kaching.util.DefaultSleeper;
import com.kaching.util.FakeSleeper;
import com.kaching.util.tests.PersistentTestBase;

public class AbstractHybridQueueTest extends PersistentTestBase {

  private final DateTime now = new DateTime(2016, 2, 13, 1, 2, 3, 4, ET);

  @BeforeClass
  public static void beforeClass() {
    configure(MockEntity.class);
  }

  @Test
  public void standardTest_setWorkerCount() {
    AbstractHybridQueue<?, ?> queue = new TestHybridQueue(3, new HashMap<>());
    assertEquals(3, queue.getWorkerCount());

    queue.setWorkerCount(7);
    assertEquals(7, queue.getWorkerCount());
    assertEquals(7, queue.executor.getMaximumPoolSize());

    assertThrows(InvalidArgumentException.class, "worker count must be positive", () -> queue.setWorkerCount(0));
    assertEquals(7, queue.getWorkerCount());
    assertEquals(7, queue.executor.getMaximumPoolSize());

    queue.setWorkerCount(5);
    assertEquals(5, queue.getWorkerCount());
    assertEquals(5, queue.executor.getMaximumPoolSize());
  }

  @Test
  public void loadUnsentFromDatabase() {
    Id<MockEntity> id1 = transacter.save(new MockEntity());
    Id<MockEntity> id2 = transacter.save(new MockEntity());
    Id<MockEntity> id3 = transacter.save(new MockEntity());
    Id<MockEntity> id4 = transacter.save(new MockEntity());

    List<Id<MockEntity>> allIds = Arrays.asList(id1, id2, id3, id4);
    Collections.sort(allIds);
    Map<Id<MockEntity>, WrappedComparable> expectedIds = ImmutableMap.of(
        allIds.get(0), new WrappedComparable(null, allIds.get(0).getId()),
        allIds.get(1), new WrappedComparable(null, allIds.get(1).getId()),
        allIds.get(2), new WrappedComparable(null, allIds.get(2).getId())
    );

    assertEquals(expectedIds, getQueue().loadUnsentFromDatabase(3));
  }

  @Test
  public void pausingTest_temporaryException_shouldNotPause() {
    AtomicBoolean itemWasSent = new AtomicBoolean(false);
    AtomicReference<DateTime> currentTime = new AtomicReference<>(now);
    TestHybridQueue queue = getQueueForPausingTest(itemWasSent, currentTime);
    MockEntity mockEntity = new MockEntity();
    mockEntity.setLocalId(5);
    Id<MockEntity> id = transacter.save(mockEntity);

    queue.handleExceptionInSend(new TimeoutException(), id, 5, 0);
    assertFalse(queue.pausedCategories.contains(1));

    new AbstractHybridQueue.ProcessSingleItem<>(id, 0, queue, new WrappedComparable(0, 0)).run();

    assertTrue(itemWasSent.get());
    assertEquals(0, queue.delayedEnqueues.size());
  }

  @Test
  public void pausingTest_shouldPauseException_shouldPause() {
    AtomicBoolean itemWasSent = new AtomicBoolean(false);
    AtomicReference<DateTime> currentTime = new AtomicReference<>(now);
    TestHybridQueue queue = getQueueForPausingTest(itemWasSent, currentTime);
    MockEntity mockEntity = new MockEntity();
    mockEntity.setLocalId(5);
    Id<MockEntity> id = transacter.save(mockEntity);

    queue.handleExceptionInSend(new ResolutionException(), id, 5, 0);
    assertTrue(queue.pausedCategories.contains(1));

    AbstractHybridQueue.ProcessSingleItem<MockEntity, Integer> singleItem =
        new AbstractHybridQueue.ProcessSingleItem<>(id, 0, queue, new WrappedComparable(0, 0));

    singleItem.run();

    assertFalse(itemWasSent.get());
    assertEquals(1, queue.delayedEnqueues.size());
  }

  @Test
  public void pausingTest_timePassing_shouldUnpause() {
    AtomicBoolean itemWasSent = new AtomicBoolean(false);
    AtomicReference<DateTime> currentTime = new AtomicReference<>(now);
    TestHybridQueue queue = getQueueForPausingTest(itemWasSent, currentTime);
    MockEntity mockEntity = new MockEntity();
    mockEntity.setLocalId(5);
    Id<MockEntity> id = transacter.save(mockEntity);

    queue.handleExceptionInSend(new ResolutionException(), id, 5, 0);
    assertTrue(queue.pausedCategories.contains(1));
    currentTime.set(now.plusSeconds(queue.getLongDelayLengthMillis() + 1));
    assertFalse(queue.pausedCategories.contains(1));

    AbstractHybridQueue.ProcessSingleItem<MockEntity, Integer> singleItem =
        new AbstractHybridQueue.ProcessSingleItem<>(id, 0, queue, new WrappedComparable(0, 0));

    singleItem.run();

    assertTrue(itemWasSent.get());
    assertEquals(0, queue.delayedEnqueues.size());
  }

  @Test
  public void singleItemShouldRun() {
    Map<Integer, Integer> sentItems = new HashMap<>();
    TestHybridQueue queue = new TestHybridQueue(1, sentItems);
    setupQueue(queue);

    MockEntity mockEntity = new MockEntity();
    mockEntity.setLocalId(5);
    Id<MockEntity> id = transacter.save(mockEntity);

    new AbstractHybridQueue.ProcessSingleItem<>(id, 0, queue, new WrappedComparable(0, 0)).run();

    assertEquals(ImmutableMap.of(5, 1), sentItems);
    transacter.executeWithReadOnlySession(session -> {
      MockEntity entity = session.getOrThrow(MockEntity.class, id);
      assertNotNull(entity.getSentTime());
    });
  }

  @Test
  public void singleItemShouldRun_whenTransformReturnsNull() {
    Map<Integer, Integer> sentItems = new HashMap<>();
    TestHybridQueue queue = new TestHybridQueue(1, sentItems);
    setupQueue(queue);

    MockEntity mockEntity = new MockEntity();
    mockEntity.setLocalId(null);
    Id<MockEntity> id = transacter.save(mockEntity);

    new AbstractHybridQueue.ProcessSingleItem<>(id, 0, queue, new WrappedComparable(0, 0)).run();

    Map<Integer, Integer> expected = new HashMap<>();
    expected.put(null, 1);
    assertEquals(expected, sentItems);
    transacter.executeWithReadOnlySession(session -> {
      MockEntity entity = session.getOrThrow(MockEntity.class, id);
      assertNotNull(entity.getSentTime());
    });
  }

  @Test
  public void nonexistent_singleItemShouldNotRun() {
    Map<Integer, Integer> sentItems = new HashMap<>();
    TestHybridQueue queue = new TestHybridQueue(1, sentItems);
    setupQueue(queue);

    new AbstractHybridQueue.ProcessSingleItem<>(Id.of(13), 0, queue, new WrappedComparable(0, 0)).run();

    assertEquals(emptyMap(), sentItems);
  }

  @Test
  public void alreadySent_singleItemShouldNotRun() {
    Map<Integer, Integer> sentItems = new HashMap<>();
    TestHybridQueue queue = new TestHybridQueue(1, sentItems);
    setupQueue(queue);
    queue.shutdownGracefully(false);

    MockEntity mockEntity = new MockEntity(5);
    mockEntity.setSentTime(now.minusMinutes(4));
    Id<MockEntity> id = transacter.save(mockEntity);

    new AbstractHybridQueue.ProcessSingleItem<>(id, 0, queue, new WrappedComparable(0, 0)).run();

    assertEquals(emptyMap(), sentItems);
    transacter.executeWithReadOnlySession(session -> {
      MockEntity entity = session.getOrThrow(MockEntity.class, id);
      assertEquals(now.minusMinutes(4), entity.getSentTime());
    });
  }

  @Test
  public void ignored_singleItemShouldNotRun() {
    Map<Integer, Integer> sentItems = new HashMap<>();
    TestHybridQueue queue = new TestHybridQueue(1, sentItems);
    setupQueue(queue);
    queue.shutdownGracefully(false);

    MockEntity mockEntity = new MockEntity(5);
    mockEntity.setIgnoredAt(now);
    Id<MockEntity> id = transacter.save(mockEntity);

    new AbstractHybridQueue.ProcessSingleItem<>(id, 0, queue, new WrappedComparable(0, 0)).run();

    assertEquals(emptyMap(), sentItems);
    transacter.executeWithReadOnlySession(session -> {
      MockEntity entity = session.getOrThrow(MockEntity.class, id);
      assertEquals(IGNORED_AT_DUMMY_VALUE, entity.getSentTime());
    });
  }

  @Test
  public void errorFlag_singleItemShouldNotRun() {
    Map<Integer, Integer> sentItems = new HashMap<>();
    TestHybridQueue queue = new TestHybridQueue(1, sentItems);
    setupQueue(queue);
    queue.shutdownGracefully(false);

    MockEntity mockEntity = new MockEntity(5);
    mockEntity.setErrorFlag(true);
    Id<MockEntity> id = transacter.save(mockEntity);

    new AbstractHybridQueue.ProcessSingleItem<>(id, 0, queue, new WrappedComparable(0, 0)).run();

    assertEquals(emptyMap(), sentItems);
    transacter.executeWithReadOnlySession(session -> {
      MockEntity entity = session.getOrThrow(MockEntity.class, id);
      assertNull(entity.getSentTime());
    });
  }

  @Test
  public void shouldShutdown_singleItemShouldNotRun() {
    Map<Integer, Integer> sentItems = new HashMap<>();
    TestHybridQueue queue = new TestHybridQueue(1, sentItems);
    setupQueue(queue);
    queue.shutdownGracefully(false);

    MockEntity mockEntity = new MockEntity();
    mockEntity.setLocalId(5);
    Id<MockEntity> id = transacter.save(mockEntity);

    new AbstractHybridQueue.ProcessSingleItem<>(id, 0, queue, new WrappedComparable(0, 0)).run();

    assertEquals(emptyMap(), sentItems);
    transacter.executeWithReadOnlySession(session -> {
      MockEntity entity = session.getOrThrow(MockEntity.class, id);
      assertNull(entity.getSentTime());
    });
  }

  @Test
  public void isLeaderFalse_singleItemShouldNotRun() {
    Map<Integer, Integer> sentItems = new HashMap<>();
    TestHybridQueue queue = new TestHybridQueue(1, sentItems);
    setupQueue(queue);
    queue.isLeader = () -> false;

    MockEntity mockEntity = new MockEntity();
    mockEntity.setLocalId(5);
    Id<MockEntity> id = transacter.save(mockEntity);

    new AbstractHybridQueue.ProcessSingleItem<>(id, 0, queue, new WrappedComparable(0, 0)).run();

    assertEquals(emptyMap(), sentItems);
    transacter.executeWithReadOnlySession(session -> {
      MockEntity entity = session.getOrThrow(MockEntity.class, id);
      assertNull(entity.getSentTime());
    });
  }

  @Test
  public void isLeaderFalse_notLeaderOnly_singleItemShouldRun() {
    Map<Integer, Integer> sentItems = new HashMap<>();
    TestHybridQueue queue = new TestHybridQueue(1, sentItems) {
      @Override
      protected boolean leaderOnly() {
        return false;
      }
    };
    setupQueue(queue);
    queue.isLeader = () -> false;

    MockEntity mockEntity = new MockEntity();
    mockEntity.setLocalId(5);
    Id<MockEntity> id = transacter.save(mockEntity);

    new AbstractHybridQueue.ProcessSingleItem<>(id, 0, queue, new WrappedComparable(0, 0)).run();

    assertEquals(ImmutableMap.of(5, 1), sentItems);
    transacter.executeWithReadOnlySession(session -> {
      MockEntity entity = session.getOrThrow(MockEntity.class, id);
      assertNotNull(entity.getSentTime());
    });
  }

  @Test
  public void isMockFailoverFakeMasterTrue_singleItemShouldNotRun() {
    TestHybridQueue queue = getQueue();
    queue.multiColoStatusProvider = () -> MOCK_FAILOVER_FAKE_MASTER;

    MockEntity mockEntity = new MockEntity();
    mockEntity.setLocalId(5);
    Id<MockEntity> id = transacter.save(mockEntity);

    new AbstractHybridQueue.ProcessSingleItem<>(id, 0, queue, new WrappedComparable(0, 0)).run();

    transacter.executeWithReadOnlySession(session -> {
      MockEntity entity = session.getOrThrow(MockEntity.class, id);
      assertNull(entity.getSentTime());
    });
  }

  @Test
  public void isMockFailoverFakeMasterTrue_shouldExecute_singleItemShouldRun() {
    TestHybridQueue queue = new TestHybridQueue(1, new HashMap<>()) {
      @Override
      protected boolean shouldNotExecuteOnMockFailoverFakeMaster() {
        return false;
      }
    };
    setupQueue(queue);
    queue.multiColoStatusProvider = () -> MOCK_FAILOVER_FAKE_MASTER;

    MockEntity mockEntity = new MockEntity();
    mockEntity.setLocalId(5);
    Id<MockEntity> id = transacter.save(mockEntity);

    new AbstractHybridQueue.ProcessSingleItem<>(id, 0, queue, new WrappedComparable(0, 0)).run();

    transacter.executeWithReadOnlySession(session -> {
      MockEntity entity = session.getOrThrow(MockEntity.class, id);
      assertNotNull(entity.getSentTime());
    });
  }

  @Test
  public void serviceIsUnannounced_singleItemShouldNotRun() {
    TestHybridQueue queue = getQueue();
    queue.localAnnouncement = new FakeLocalAnnouncement(false);

    MockEntity mockEntity = new MockEntity();
    mockEntity.setLocalId(5);
    Id<MockEntity> id = transacter.save(mockEntity);

    new AbstractHybridQueue.ProcessSingleItem<>(id, 0, queue, new WrappedComparable(0, 0)).run();

    transacter.executeWithReadOnlySession(session -> {
      MockEntity entity = session.getOrThrow(MockEntity.class, id);
      assertNull(entity.getSentTime());
    });
  }

  @Test
  public void isLeaderChangeToFalseAfterTransform_singleItemShouldNotRun() {
    TestHybridQueue queue = new TestHybridQueue(1, new HashMap<>()) {
      @Override
      protected Integer transform(MockEntity entity, DbSession session) {
        assertEquals(true, isLeader.get());
        isLeader = () -> false;
        return super.transform(entity, session);
      }
    };
    queue.clock = Providers.of(new DateTime(2017, 2, 13, 1, 2, 3, 4, ET));
    queue.transacter = transacter;
    queue.isLeader = () -> true;
    queue.hasStarted = true;
    queue.multiColoStatusProvider = () -> MASTER;
    queue.localAnnouncement = new FakeLocalAnnouncement(true);

    MockEntity mockEntity = new MockEntity();
    mockEntity.setLocalId(5);
    Id<MockEntity> id = transacter.save(mockEntity);

    new AbstractHybridQueue.ProcessSingleItem<>(id, 0, queue, new WrappedComparable(0, 0)).run();

    transacter.executeWithReadOnlySession(session -> {
      MockEntity entity = session.getOrThrow(MockEntity.class, id);
      assertNull(entity.getSentTime());
    });
    assertEquals(false, queue.isLeader.get());
  }

  @Test
  public void persistAndEnqueue_shouldValidateItems() {
    TestHybridQueue queue = getQueue();

    MockEntity alreadyPersisted = new MockEntity();
    alreadyPersisted.setId(Id.of(123));

    MockEntity alreadySent = new MockEntity();
    alreadySent.setSentTime(now);

    MockEntity ignored = new MockEntity();
    ignored.setIgnoredAt(now);

    MockEntity errorFlag = new MockEntity();
    errorFlag.setErrorFlag(true);

    assertThrows(IllegalArgumentException.class, () -> queue.persistAndEnqueue(alreadyPersisted, null));
    assertThrows(IllegalArgumentException.class, () -> queue.persistAndEnqueue(alreadySent, null));
    assertThrows(IllegalArgumentException.class, () -> queue.persistAndEnqueue(ignored, null));
    assertThrows(IllegalArgumentException.class, () -> queue.persistAndEnqueue(errorFlag, null));
  }

  @Test
  public void signalEnqueue_shouldNotExecute_returnsFalse() {
    TestHybridQueue testHybridQueue = getQueue();
    testHybridQueue.hasStarted = false;

    assertFalse(testHybridQueue.signalEnqueue(Id.of(5)));

    testHybridQueue.hasStarted = true;
    testHybridQueue.isLeader = Providers.of(false);

    assertFalse(testHybridQueue.signalEnqueue(Id.of(5)));

    testHybridQueue.isLeader = Providers.of(true);
    testHybridQueue.shutdownGracefully(false);

    assertFalse(testHybridQueue.signalEnqueue(Id.of(5)));

    testHybridQueue = getQueue();
    testHybridQueue.hasStarted = true;
    testHybridQueue.isLeader = Providers.of(true);
    testHybridQueue.localAnnouncement = new FakeLocalAnnouncement(false);

    assertFalse(testHybridQueue.signalEnqueue(Id.of(5)));

    testHybridQueue.localAnnouncement = new FakeLocalAnnouncement(true);
    testHybridQueue.multiColoStatusProvider = () -> MOCK_FAILOVER_FAKE_MASTER;

    assertFalse(testHybridQueue.signalEnqueue(Id.of(5)));
  }

  @Test
  public void signalEnqueue_notAlreadyEnqueued_throws() {
    final TestHybridQueue testHybridQueue = getQueue();
    testHybridQueue.hasStarted = true;
    testHybridQueue.isLeader = Providers.of(true);
    testHybridQueue.localAnnouncement = new FakeLocalAnnouncement(true);
    testHybridQueue.multiColoStatusProvider = () -> MASTER;

    assertThrows(
        NotFoundException.class,
        "Do not signal enqueue an entity that has not already been persisted",
        () -> testHybridQueue.signalEnqueue(Id.of(5))
    );
  }

  @Test
  public void signalEnqueue_setsPolledTime_enqueues() {
    final TestHybridQueue testHybridQueue = getQueue();
    testHybridQueue.hasStarted = true;
    testHybridQueue.isLeader = Providers.of(true);
    testHybridQueue.localAnnouncement = new FakeLocalAnnouncement(true);
    testHybridQueue.multiColoStatusProvider = () -> MASTER;
    testHybridQueue.clock = Providers.of(now);

    MockEntity mockEntity = new MockEntity();
    Id<MockEntity> mockEntityId = transacter.save(mockEntity);

    assertTrue(testHybridQueue.signalEnqueue(mockEntityId));

    assertEquals(Set.of(mockEntityId), testHybridQueue.enqueuedIds);
  }

  @Test
  public void shouldEnqueueEntity_isFalse_justPersists() {
    AtomicBoolean calledEnqueue = new AtomicBoolean(false);
    AtomicBoolean calledShouldEnqueue = new AtomicBoolean(false);
    TestHybridQueue queue = new TestHybridQueue(1, new HashMap<>()) {
      @Override
      protected boolean shouldEnqueueEntity(MockEntity entity) {
        calledShouldEnqueue.set(true);
        return false;
      }

      @Override
      void enqueueSingleItem(ProcessSingleItem<MockEntity, Integer> processSingleItem) {
        calledEnqueue.set(true);
      }
    };
    queue.clock = Providers.of(new DateTime(2017, 2, 13, 1, 2, 3, 4, ET));
    queue.transacter = transacter;
    queue.isLeader = () -> true;
    queue.hasStarted = true;
    queue.multiColoStatusProvider = () -> MASTER;
    queue.localAnnouncement = new FakeLocalAnnouncement(true);

    MockEntity mockEntity = new MockEntity();
    mockEntity.setLocalId(5);

    transacter.executeWithSession(session -> queue.persistAndEnqueue(mockEntity, session));

    assertTrue(calledShouldEnqueue.get());
    assertFalse(calledEnqueue.get());

    transacter.executeWithReadOnlySession(session -> {
      MockEntity entity = session.createCriteria(MockEntity.class).uniqueResult();
      assertNull(entity.getPolledTime());
    });
  }

  @Test
  public void shouldEnqueueEntity_isTrue_persistsAndEnqueues() {
    AtomicBoolean calledEnqueue = new AtomicBoolean(false);
    AtomicBoolean calledShouldEnqueue = new AtomicBoolean(false);
    TestHybridQueue queue = new TestHybridQueue(1, new HashMap<>()) {
      @Override
      protected boolean shouldEnqueueEntity(MockEntity entity) {
        calledShouldEnqueue.set(true);
        return true;
      }

      @Override
      void enqueueSingleItem(ProcessSingleItem<MockEntity, Integer> processSingleItem) {
        calledEnqueue.set(true);
      }
    };
    queue.clock = Providers.of(new DateTime(2017, 2, 13, 1, 2, 3, 4, ET));
    queue.transacter = transacter;
    queue.isLeader = () -> true;
    queue.hasStarted = true;
    queue.multiColoStatusProvider = () -> MASTER;
    queue.localAnnouncement = new FakeLocalAnnouncement(true);

    MockEntity mockEntity = new MockEntity();
    mockEntity.setLocalId(5);

    transacter.executeWithSession(session -> queue.persistAndEnqueue(mockEntity, session));

    assertTrue(calledShouldEnqueue.get());
    assertTrue(calledEnqueue.get());

    transacter.executeWithReadOnlySession(session -> {
      MockEntity entity = session.createCriteria(MockEntity.class).uniqueResult();
      assertNotNull(entity.getPolledTime());
    });
  }

  @Test
  public void hasStartedFalse_singleItemShouldNotRun() {
    TestHybridQueue queue = new TestHybridQueue(1, new HashMap<>());
    queue.clock = Providers.of(new DateTime(2017, 2, 13, 1, 2, 3, 4, ET));
    queue.transacter = transacter;
    queue.isLeader = () -> true;
    queue.hasStarted = false;
    queue.multiColoStatusProvider = () -> MASTER;

    MockEntity mockEntity = new MockEntity();
    mockEntity.setLocalId(5);
    Id<MockEntity> id = transacter.save(mockEntity);

    new AbstractHybridQueue.ProcessSingleItem<>(id, 0, queue, new WrappedComparable(0, 0)).run();

    transacter.executeWithReadOnlySession(session -> {
      MockEntity entity = session.getOrThrow(MockEntity.class, id);
      assertNull(entity.getSentTime());
    });
  }

  @Test
  public void hasStartedFalse_queueShouldNotPollOrEnqueue() {
    AtomicBoolean calledEnqueue = new AtomicBoolean(false);
    TestHybridQueue queue = new TestHybridQueue(1, new HashMap<>()) {
      @Override
      void enqueueSingleItem(ProcessSingleItem<MockEntity, Integer> processSingleItem) {
        calledEnqueue.set(true);
      }
    };
    queue.clock = Providers.of(new DateTime(2017, 2, 13, 1, 2, 3, 4, ET));
    queue.transacter = transacter;
    queue.isLeader = () -> true;
    queue.hasStarted = false;
    queue.multiColoStatusProvider = () -> MASTER;

    MockEntity mockEntity = new MockEntity();
    mockEntity.setLocalId(5);

    transacter.executeWithSession(session -> queue.persistAndEnqueue(mockEntity, session));

    assertEquals(false, calledEnqueue.get());
    transacter.executeWithReadOnlySession(session -> {
      MockEntity entity = session.createCriteria(MockEntity.class).uniqueResult();
      assertNull(entity.getPolledTime());
    });
  }

  @Test
  public void tryLockAndExecute() {
    MockEntity mockEntity = new MockEntity();
    mockEntity.setLocalId(5);

    TestHybridQueue queue = new TestHybridQueue(1, null) {
      @Override
      protected PersistentHybridQueueEntityRepository<MockEntity> getEntityRepository(DbSession session) {
        return new PersistentHybridQueueEntityRepository<MockEntity>(MockEntity.class, session) {
          @Override
          public MockEntity getOrThrow(Id<MockEntity> id) {
            assertEquals(Id.of(5), id);
            return mockEntity;
          }
        };
      }

      @Override
      protected boolean extraTryLock(MockEntity mockEntity, DbSession session) {
        return true;
      }

      @Override
      protected void extraUnlock(MockEntity mockEntity, DbSession session) {

      }
    };

    AtomicBoolean calledExecute = new AtomicBoolean(false);
    boolean success = queue.tryLockAndExecute(Id.of(5), null, entity -> {
      assertEquals(Integer.valueOf(5), entity.getLocalId());
      calledExecute.set(true);
    });

    assertEquals(true, calledExecute.get());
    assertEquals(true, success);
  }

  @Test
  public void tryLockAndExecute_shouldNotExecuteWithoutLock() throws Exception {
    TestHybridQueue queue = new TestHybridQueue(1, null);
    Thread thread = new Thread(() -> queue.getIdLocks().get(Id.of(5)).lock());
    thread.start();
    thread.join();

    boolean success = queue.tryLockAndExecute(Id.of(5), null, entity -> fail());

    assertEquals(false, success);
  }

  @Test
  public void tryLockAndExecute_shouldNotExecuteWithoutExtraLock() {
    MockEntity mockEntity = new MockEntity();
    mockEntity.setLocalId(5);

    TestHybridQueue queue = new TestHybridQueue(1, null) {
      @Override
      protected PersistentHybridQueueEntityRepository<MockEntity> getEntityRepository(DbSession session) {
        return new PersistentHybridQueueEntityRepository<MockEntity>(MockEntity.class, session) {
          @Override
          public MockEntity getOrThrow(Id<MockEntity> id) {
            assertEquals(Id.of(5), id);
            return mockEntity;
          }
        };
      }

      @Override
      protected boolean extraTryLock(MockEntity mockEntity, DbSession session) {
        return false;
      }
    };

    boolean success = queue.tryLockAndExecute(Id.of(5), null, entity -> fail());

    assertEquals(false, success);
  }

  @Test
  public void enumValues_processOutcome() {
    AbstractHybridQueue.ProcessSingleItem<MockEntity, Integer> processSingleItem =
        new AbstractHybridQueue.ProcessSingleItem<>(Id.of(1), 0, new DoNothingQueue(), new WrappedComparable(0, 0));

    for (AbstractHybridQueue.ProcessOutcome processOutcome : AbstractHybridQueue.ProcessOutcome.values()) {
      processSingleItem.handleOutcome(processOutcome, Option.some(1));
    }
  }

  @Test
  public void isSuspended() {
    TestHybridQueue queue = new TestHybridQueue(1, null);
    assertFalse(queue.isSuspended());

    queue.suspend();
    assertTrue(queue.isSuspended());

    queue.unsuspend();
    assertFalse(queue.isSuspended());
  }

  @Test
  public void getDebugState_empty() {
    TestHybridQueue queue = getQueue();
    assertEquals("Current time: 2017-02-13T01:02:03.004-05:00\n" +
            "enqueuedIds (0): []\n" +
            "delayedEnqueues (0): []\n" +
            "locked locks: 0 of 4096\n" +
            "checkDbAt: null\n" +
            "checkDelayQueueAt: null\n" +
            "pausedCategories: []\n" +
            "executor.getQueue().size(): 0\n" +
            "executor.getActiveCount(): 0\n" +
            "executor.getTaskCount(): 0\n" +
            "shouldNotExecute(): false",
        queue.getDebugState());
  }

  @Test
  public void getDebugState_variousDetails() {
    TestHybridQueue queue = getQueue();
    Id<MockEntity> id1 = transacter.save(new MockEntity());
    Id<MockEntity> id2 = transacter.executeWithSessionExpression(session -> {
      Id<MockEntity> id = queue.persistAndEnqueue(new MockEntity(), session);
      queue.shutdown();
      queue.run();
      return id;
    });
    queue.handleExceptionInSend(new ResolutionException(), id1, 0, 0);
    queue.enqueueSingleItemWithDelay(new AbstractHybridQueue.ProcessSingleItem<>(id1, 2, queue,
        new WrappedComparable(0, 0)), 30_000);
    queue.enqueueSingleItemWithDelay(new AbstractHybridQueue.ProcessSingleItem<>(id2, 7, queue,
        new WrappedComparable(0, 0)), 90_000);
    transacter.executeWithSession(session ->
        assertEquals("Item " + id2 +
                " is not locked, is not extra-locked, is delayed for [90] seconds, has had [7] unknown exceptions, and is enqueued.\n" +
                "Current time: 2017-02-13T01:02:03.004-05:00\n" +
                "enqueuedIds (1): [" + id2 + "]\n" +
                "delayedEnqueues (2): [" + id1 + ":30s, " + id2 + ":90s]\n" +
                "locked locks: 0 of 4096\n" +
                "checkDbAt: 2017-02-13T01:02:03.054-05:00\n" +
                "checkDelayQueueAt: 2017-02-13T01:02:03.054-05:00\n" +
                "pausedCategories: [unit]\n" +
                "executor.getQueue().size(): 0\n" +
                "executor.getActiveCount(): 0\n" +
                "executor.getTaskCount(): 0\n" +
                "shouldNotExecute(): true",
            queue.getDebugState(id2, session))
    );
  }

  @Test
  public void trySafeExtraUnlock_entityNotFound_throws() {
    AtomicInteger unlockCalledCount = new AtomicInteger(0);

    TestHybridQueue queue = new TestHybridQueue(1, new HashMap<>()) {
      @Override
      protected void extraUnlock(MockEntity mockEntity, DbSession session) {
        unlockCalledCount.incrementAndGet();
      }
    };
    setupQueue(queue);

    transacter.executeWithSession(session ->
        assertThrows(
            NotFoundException.class,
            () -> queue.trySafeExtraUnlock(Id.of(12), session)
        )
    );
    assertEquals(0, unlockCalledCount.get());
  }

  @Test
  public void trySafeExtraUnlock_entityHasErrorFlagSet_callsExtraUnlock() {
    AtomicInteger unlockCalledCount = new AtomicInteger(0);
    MockEntity entity = new MockEntity(12);
    entity.setErrorFlag(true);
    Id<MockEntity> entityId = transacter.save(entity);

    TestHybridQueue queue = new TestHybridQueue(1, new HashMap<>()) {
      @Override
      protected void extraUnlock(MockEntity mockEntity, DbSession session) {
        assertEquals(entityId, mockEntity.getId());
        unlockCalledCount.incrementAndGet();
      }
    };
    setupQueue(queue);

    transacter.executeWithSession(session ->
        assertTrue(queue.trySafeExtraUnlock(entityId, session))
    );
    assertEquals(1, unlockCalledCount.get());
  }

  @Test
  public void trySafeExtraUnlock_entityIsSent_callsExtraUnlock() {
    AtomicInteger unlockCalledCount = new AtomicInteger(0);
    MockEntity entity = new MockEntity(12);
    entity.setSentTime(now);
    Id<MockEntity> entityId = transacter.save(entity);

    TestHybridQueue queue = new TestHybridQueue(1, new HashMap<>()) {
      @Override
      protected void extraUnlock(MockEntity mockEntity, DbSession session) {
        assertEquals(entityId, mockEntity.getId());
        unlockCalledCount.incrementAndGet();
      }
    };
    setupQueue(queue);

    transacter.executeWithSession(session ->
        assertTrue(queue.trySafeExtraUnlock(entityId, session))
    );
    assertEquals(1, unlockCalledCount.get());
  }

  @Test
  public void trySafeExtraUnlock_entityIsNotSentAndErrorFlagIsNotSet_doesNotCallExtraUnlock() {
    AtomicInteger unlockCalledCount = new AtomicInteger(0);
    MockEntity entity = new MockEntity(12);
    Id<MockEntity> entityId = transacter.save(entity);

    TestHybridQueue queue = new TestHybridQueue(1, new HashMap<>()) {
      @Override
      protected void extraUnlock(MockEntity mockEntity, DbSession session) {
        assertEquals(entityId, mockEntity.getId());
        unlockCalledCount.incrementAndGet();
      }
    };
    setupQueue(queue);

    transacter.executeWithSession(session ->
        assertFalse(queue.trySafeExtraUnlock(entityId, session))
    );
    assertEquals(0, unlockCalledCount.get());
  }

  @Test
  public void exceptionInRun_shouldContinue() {
    TestHybridQueue queue = getQueue();
    FakeStackTraceMonitor fakeStm = new FakeStackTraceMonitor();
    queue.stackTraceMonitor = fakeStm;
    queue.isLeader = new Provider<Boolean>() {
      private int numExceptions = 5;

      @Override
      public Boolean get() {
        if (numExceptions-- > 0) {
          throw new RuntimeException("oh no");
        }
        return true;
      }
    };
    queue.shutdown();
    queue.run();

    Map.Entry<String, Integer> stackTraces = Iterables.getOnlyElement(fakeStm.getStackTracesAndClear());
    assertThat(stackTraces.getKey(), containsString("Unhandled exception in queue main thread"));
    assertThat(stackTraces.getKey(), containsString("oh no"));
    assertEquals(5, stackTraces.getValue().intValue());
  }

  @Test
  public void defaultOrder_isIdOrder() {
    DefaultOrderingHybridQueue queue = new DefaultOrderingHybridQueue();
    setupQueueForOrderingTest(queue);

    transacter.save(new MockEntity(11));
    transacter.save(new MockEntity(33));
    transacter.save(new MockEntity(22));
    transacter.save(new MockEntity(44));

    runQueueInOtherThreadTillCompletion(queue, 4);
    Set<Integer> localIdsSent = ImmutableSet.of(11, 33, 22, 44);
    assertTrue(queue.localIdsSent.containsAll(localIdsSent));
  }

  @Test
  public void customOrderQueue_followsCustomOrder() {
    LocalIdOrderingHybridQueue queue = new LocalIdOrderingHybridQueue();
    setupQueueForOrderingTest(queue);

    transacter.save(new MockEntity(11));
    transacter.save(new MockEntity(33));
    transacter.save(new MockEntity(22));
    transacter.save(new MockEntity(44));

    runQueueInOtherThreadTillCompletion(queue, 4);

    assertEquals(ImmutableList.of(11, 22, 33, 44), queue.localIdsSent);
  }

  @Test
  public void customOrderQueue_failToCorrectlyWriteBulkIdGetter_addsToStm_hasDefaultOrdering() {
    transacter.save(new MockEntity(11));
    transacter.save(new MockEntity(33));
    Id<MockEntity> id2 = transacter.save(new MockEntity(22));
    transacter.save(new MockEntity(44));

    LocalIdOrderingHybridQueue queue = new LocalIdOrderingHybridQueue() {
      @Override
      protected Map<Id<MockEntity>, Comparable<?>> bulkGetCustomComparables(
          Set<Id<MockEntity>> ids, DbSession session) {
        Map<Id<MockEntity>, Comparable<?>> incorrectResult = super.bulkGetCustomComparables(ids, session);
        incorrectResult.remove(id2);
        return incorrectResult;
      }
    };
    setupQueueForOrderingTest(queue);
    runQueueInOtherThreadTillCompletion(queue, 4);

    assertEquals(4, queue.localIdsSent.size());
    List<Integer> resultsWithout22 = new ArrayList<>(queue.localIdsSent);
    resultsWithout22.removeIf(i -> i == 22);

    assertEquals(ImmutableList.of(11, 33, 44), resultsWithout22);
  }

  @Test
  public void recordProcessedIgnoredId() {
    LocalIdOrderingHybridQueue queue = new LocalIdOrderingHybridQueue();

    assertEmpty(queue.getProcessedIgnoredIds());
    assertEquals(0, queue.getProcessedIgnoredCount());

    queue.recordProcessedIgnoredId(Id.of(12));

    assertEquals(ImmutableSet.of(Id.of(12)), queue.getProcessedIgnoredIds());
    assertEquals(1, queue.getProcessedIgnoredCount());

    queue.recordProcessedIgnoredId(Id.of(13));

    assertEquals(ImmutableSet.of(Id.of(12), Id.of(13)), queue.getProcessedIgnoredIds());
    assertEquals(2, queue.getProcessedIgnoredCount());

    queue.recordProcessedIgnoredId(Id.of(13));

    assertEquals(ImmutableSet.of(Id.of(12), Id.of(13)), queue.getProcessedIgnoredIds());
    assertEquals(3, queue.getProcessedIgnoredCount());

    queue.resetProcessedIgnoredIds();

    assertEmpty(queue.getProcessedIgnoredIds());
    assertEquals(0, queue.getProcessedIgnoredCount());

    queue.recordProcessedIgnoredId(Id.of(13));

    assertEquals(ImmutableSet.of(Id.of(13)), queue.getProcessedIgnoredIds());
    assertEquals(1, queue.getProcessedIgnoredCount());
  }

  @Test
  public void wrappedComparableOrdering_withAllComparables() {
    List<WrappedComparable> input = ImmutableList.of(
        new WrappedComparable("ZZ", 0),
        new WrappedComparable("CC", -100),
        new WrappedComparable("DD", 1000),
        new WrappedComparable("BB", 0)
    );
    List<String> result = input.stream()
        .sorted()
        .map(c -> (String) c.getCustomComparable())
        .collect(Collectors.toList());
    assertEquals(ImmutableList.of("BB", "CC", "DD", "ZZ"), result);
  }

  @Test
  public void wrappedComparableOrdering_withAllFallbacks() {
    List<WrappedComparable> input = ImmutableList.of(
        new WrappedComparable(null, 11),
        new WrappedComparable(null, 33),
        new WrappedComparable(null, 22)
    );
    List<Long> result = input.stream()
        .sorted()
        .map(c -> c.getId())
        .collect(Collectors.toList());
    assertEquals(ImmutableList.of(11L, 22L, 33L), result);
  }

  @Test
  public void wrappedComparableOrdering_withSomeErrors_putsErrorsLast() {
    WrappedComparable wc11 = new WrappedComparable(null, 11);
    WrappedComparable wc33 = new WrappedComparable(null, 33);
    WrappedComparable wcDD = new WrappedComparable("DD", -1000);
    WrappedComparable wcBB = new WrappedComparable("BB", 100);
    WrappedComparable wc22 = new WrappedComparable(null, 22);
    List<WrappedComparable> input = ImmutableList.of(
        wc11,
        wc33,
        wcDD,
        wcBB,
        wc22
    );
    List<WrappedComparable> result = input.stream()
        .sorted()
        .collect(Collectors.toList());
    assertEquals(ImmutableList.of(wcBB, wcDD, wc11, wc22, wc33), result);
  }

  @Test
  public void processSingleItem_idLockAndExtraLockSuccessfullyAcquired_exceptionWhenUnlockingExtraLock_idLockUnlocked() {
    LockTestingHybridQueue queue = new LockTestingHybridQueue();
    setupQueueForOrderingTest(queue);

    Id<MockEntity> id = transacter.save(new MockEntity(11));

    runQueueInOtherThreadTillCompletion(queue, 1);

    assertEquals(ImmutableList.of(id), queue.lockedEntities);
    for (int i = 0; i < queue.getIdLocks().size(); i++) {
      assertTrue(queue.getIdLocks().getAt(i).tryLock());
    }
  }

  private void runQueueInOtherThreadTillCompletion(AbstractHybridQueue<?, ?> queue, int unsuspendOnQueueSize) {
    queue.executor.submit(() -> {
      // Suspend functionality doesn't seem to always work on the very first item executed. Enter no-op task.
    });
    queue.suspend();
    Thread thread = new Thread(queue);
    thread.start();

    long fiveSecondsNanos = 5 * 1_000_000_000L;
    long millisStart = System.nanoTime();
    while (System.nanoTime() - millisStart <= fiveSecondsNanos) {
      if (queue.executor.getQueue().size() >= unsuspendOnQueueSize) {
        queue.unsuspend();
      }
      boolean finished = transacter.executeWithReadOnlySessionExpression(session -> {
        HybridQueueEntityRepository<?> repo = queue.getEntityRepository(session);
        return repo.getSomeUnpolledIds(1).isEmpty() && repo.getSomePolledUnsentIds(1).isEmpty();
      });
      if (finished) {
        queue.shutdownGracefully(true);
        Unchecked.run(thread::join);
        return;
      }
      if (!thread.isAlive()) {
        throw new RuntimeException("queue quit unexpectedly");
      }
      new DefaultSleeper().sleep(Duration.millis(200));
    }
    queue.shutdown();
    thread.interrupt();
    Unchecked.run(thread::join);
    throw new RuntimeException("failed to run to completion");
  }

  private TestHybridQueue getQueue() {
    TestHybridQueue queue = new TestHybridQueue(1, new HashMap<>());
    setupQueue(queue);
    return queue;
  }

  private void setupQueue(AbstractHybridQueue<?, ?> queue) {
    queue.clock = Providers.of(new DateTime(2017, 2, 13, 1, 2, 3, 4, ET));
    queue.transacter = transacter;
    queue.isLeader = () -> true;
    queue.hasStarted = true;
    queue.multiColoStatusProvider = () -> MASTER;
    queue.localAnnouncement = new FakeLocalAnnouncement(true);
    queue.sleeper = new FakeSleeper();
  }

  private void setupQueueForOrderingTest(AbstractHybridQueue<?, ?> queue) {
    AtomicReference<DateTime> clock = new AtomicReference<>(now);
    queue.clock = () -> clock.accumulateAndGet(null, (dateTime, ignored) -> dateTime.plusMillis(200));
    queue.transacter = transacter;
    queue.isLeader = () -> true;
    queue.hasStarted = true;
    queue.multiColoStatusProvider = () -> MASTER;
    queue.localAnnouncement = new FakeLocalAnnouncement(true);
    queue.sleeper = new FakeSleeper();
  }

  private TestHybridQueue getQueueForPausingTest(
      AtomicBoolean itemWasSent, AtomicReference<DateTime> currentTime) {
    TestHybridQueue queue = new TestHybridQueue(1, new HashMap<>()) {
      @Override
      protected void send(Integer sendValue, Id<MockEntity> id) {
        itemWasSent.set(true);
      }

      @Override
      protected Object categorizeForPausing(Integer transformedValue) {
        return transformedValue % 2;
      }
    };
    queue.clock = currentTime::get;
    queue.transacter = transacter;
    queue.isLeader = () -> true;
    queue.hasStarted = true;
    queue.multiColoStatusProvider = () -> MASTER;
    queue.localAnnouncement = new FakeLocalAnnouncement(true);
    return queue;
  }

  public static class DoNothingQueue extends AbstractHybridQueue<MockEntity, Integer> {

    public DoNothingQueue() {
      super(1);
      executor.suspend();
    }

    @Override
    protected HybridQueueEntityRepository<MockEntity> getEntityRepository(DbSession session) {
      return null;
    }

    @Override
    protected Integer transform(MockEntity entity, DbSession session) {
      return null;
    }

    @Override
    protected void send(Integer sendValue, Id<MockEntity> id) {
    }

    @Override
    void enqueueSingleItemWithDelay(ProcessSingleItem<MockEntity, Integer> processSingleItem, int delayMillis) {
    }

  }

  public static class DefaultOrderingHybridQueue extends AbstractHybridQueue<MockEntity, Integer> {

    private final List<Integer> localIdsSent = new ArrayList<>();

    public DefaultOrderingHybridQueue() {
      super(1);
    }

    @Override
    protected PersistentHybridQueueEntityRepository<MockEntity> getEntityRepository(DbSession session) {
      return new PersistentHybridQueueEntityRepository<MockEntity>(MockEntity.class, session) {};
    }

    @Override
    protected Integer transform(MockEntity entity, DbSession session) {
      return entity.getLocalId();
    }

    @Override
    protected void send(Integer sendValue, Id<MockEntity> id) {
      synchronized (localIdsSent) {
        localIdsSent.add(sendValue);
      }
    }

    @Override
    protected int getMinDbPollIntervalMillis() {
      return 10;
    }

    @Override
    protected int getMaxPollIntervalMillis() {
      return 50;
    }

  }

  public static class LocalIdOrderingHybridQueue extends AbstractHybridQueue<MockEntity, Integer> {

    private final List<Integer> localIdsSent = new ArrayList<>();

    public LocalIdOrderingHybridQueue() {
      super(1);
    }

    @Override
    protected Comparable<?> getCustomComparable(MockEntity entity, DbSession session) {
      return entity.getLocalId().toString();
    }

    @Override
    protected Map<Id<MockEntity>, Comparable<?>> bulkGetCustomComparables(Set<Id<MockEntity>> ids, DbSession session) {
      return new PersistentHybridQueueEntityRepository<MockEntity>(MockEntity.class, session) {}
          .get(ids)
          .stream()
          .map(entity -> Pair.<Id<MockEntity>, Comparable<?>>of(entity.getId(), entity.getLocalId().toString()))
          .collect(pairsToMap());
    }

    @Override
    protected PersistentHybridQueueEntityRepository<MockEntity> getEntityRepository(DbSession session) {
      return new PersistentHybridQueueEntityRepository<MockEntity>(MockEntity.class, session) {};
    }

    @Override
    protected Integer transform(MockEntity entity, DbSession session) {
      return entity.getLocalId();
    }

    @Override
    protected void send(Integer sendValue, Id<MockEntity> id) {
      synchronized (localIdsSent) {
        localIdsSent.add(sendValue);
      }
    }

    @Override
    protected int getMinDbPollIntervalMillis() {
      return 10;
    }

    @Override
    protected int getMaxPollIntervalMillis() {
      return 50;
    }

  }

  public static class LockTestingHybridQueue extends AbstractHybridQueue<MockEntity, Integer> {

    protected LockTestingHybridQueue() {
      super(1);
    }

    public List<Id<MockEntity>> lockedEntities = new ArrayList<>();

    @Override
    protected HybridQueueEntityRepository<MockEntity> getEntityRepository(DbSession session) {
      return new PersistentHybridQueueEntityRepository<>(MockEntity.class, session);
    }

    @Override
    protected Integer transform(MockEntity entity, DbSession session) {
      return 5;
    }

    @Override
    protected void send(Integer sendValue, Id<MockEntity> id) {}

    @Override
    protected boolean extraTryLock(MockEntity entity, DbSession session) {
      lockedEntities.add(entity.getId());
      return true;
    }

    @Override
    protected void extraUnlock(MockEntity entity, DbSession session) {
      throw new IllegalStateException("Exception while unlocking extra lock");
    }

  }

}