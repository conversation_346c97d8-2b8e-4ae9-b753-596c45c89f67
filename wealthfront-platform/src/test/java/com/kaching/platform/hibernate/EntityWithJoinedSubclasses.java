package com.kaching.platform.hibernate;

public abstract class EntityWithJoinedSubclasses extends AbstractHibernateEntity {

  private Id<EntityWithJoinedSubclasses> id;
  @SuppressWarnings("unused")
  private boolean active;

  @Override
  public Id<EntityWithJoinedSubclasses> getId() {
    return id;
  }

  public static class Subclass1 extends EntityWithJoinedSubclasses {

    @SuppressWarnings("unused")
    private String type;

  }
}
