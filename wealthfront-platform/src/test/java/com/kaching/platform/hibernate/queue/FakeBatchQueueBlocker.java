package com.kaching.platform.hibernate.queue;

import java.util.Map;

public class FakeBatchQueueBlocker<T extends BatchQueue<?, ?>> implements BatchQueueBlocker<T> {
  
  private final FakeBatchQueueRunner<?, ?> runner;

  public FakeBatchQueueBlocker(FakeBatchQueueRunner<?, ?> runner) {
    this.runner = runner;
  }

  @Override
  public boolean blockUntilItemsProcessed(String processName, BlockingConfig config) {
    runner.runAndHandleResultsUntilEmpty();
    Map<?, String> erroredItems = runner.getErroredItemMessages();
    if (config.getMaxFailedItems() >= erroredItems.size()) {
      return true;
    }
    throw new AssertionError("Failed items causing FakeBatchQueueBlocker to block forever: " + erroredItems);
  }
  
}
