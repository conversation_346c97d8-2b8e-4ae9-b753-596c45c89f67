package com.kaching.platform.hibernate;

import static com.wealthfront.test.Assert.assertThrows;

import org.junit.Test;

public class ThrowingHibernateEntityCheckerTest {

  @Test
  public void handleException() {
    ThrowingHibernateEntityChecker checker = new ThrowingHibernateEntityChecker();
    assertThrows(IllegalArgumentException.class, () -> checker.handleException(new IllegalArgumentException()));
  }

}
