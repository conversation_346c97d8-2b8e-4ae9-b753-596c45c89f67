package com.kaching.platform.hibernate.queue.impl;

import static com.kaching.platform.hibernate.queue.impl.BatchQueueEntityFactory.createBatch;
import static com.kaching.platform.hibernate.queue.impl.BatchQueueEntityFactory.createItem;
import static com.kaching.platform.hibernate.queue.impl.BatchQueueEntityFactory.createName;
import static com.wealthfront.test.Assert.assertOptionEquals;
import static com.wealthfront.test.Assert.assertThrows;
import static com.wealthfront.util.time.DateTimeZones.ET;
import static org.junit.Assert.assertEquals;

import java.util.List;

import org.joda.time.DateTime;
import org.junit.Test;

import com.google.common.collect.ImmutableSet;
import com.google.inject.Inject;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.WithReadOnlySession;
import com.kaching.platform.hibernate.queue.BatchQueueBinder;
import com.kaching.platform.queryengine.exceptions.InvalidArgumentException;
import com.twolattes.json.Json;

public class BatchQueueSeparateItemsByValueTest extends BatchQueueTestBase {
  
  private final DateTime now = new DateTime(2012, 2, 2, 0, 0, 0, ET);

  @Test
  public void process_success() {
    Id<BatchQueueItem> item1 = createItem()
        .withQueueId(getUserIdQueueId())
        .withPayload(Json.number(11))
        .buildAndPersist(transacter())
        .getId();
    Id<BatchQueueItem> item2 = createItem()
        .withQueueId(getUserIdQueueId())
        .withPayload(Json.number(22))
        .buildAndPersist(transacter())
        .getId();
    Id<BatchQueueItem> item3 = createItem()
        .withQueueId(getUserIdQueueId())
        .withPayload(Json.number(33))
        .buildAndPersist(transacter())
        .getId();
    Id<BatchQueueItem> item4 = createItem()
        .withQueueId(getUserIdQueueId())
        .withPayload(Json.number(44))
        .buildAndPersist(transacter())
        .getId();
    Id<BatchQueueItem> item5 = createItem()
        .withQueueId(getUserIdQueueId())
        .withPayload(Json.number(55))
        .buildAndPersist(transacter())
        .getId();

    BatchQueueBatch original = createBatch()
        .withQueueId(getUserIdQueueId())
        .withItemIdFrom(item1)
        .withItemIdTo(item5)
        .withIgnoredAt(now, "ignored")
        .buildAndPersist(transacter());
    BatchQueueLockAndExecuteResult result =
        prepareQuery(new BatchQueueSeparateItemsByValue("UserIdQueue", original.getId(), 
            ImmutableSet.of(Json.number(22), Json.number(44), Json.number(55)))).process();
    assertEquals(ImmutableSet.of(original.getId()), result.getProcessedIds());

    transacter().execute(new WithReadOnlySession() {
      @Inject BatchQueueBatchRepository repository;
      @Override
      public void run(DbSession session) {
        List<BatchQueueBatch> all = repository.getAll();
        assertEquals(4, all.size());
        {
          BatchQueueBatch batch = all.get(0);
          assertEquals(original.getId(), batch.getId());
          assertEquals(item1, batch.getItemIdFrom());
          assertEquals(item1, batch.getItemIdTo());
        }
        {
          BatchQueueBatch batch = all.get(1);
          assertEquals(item2, batch.getItemIdFrom());
          assertEquals(item2, batch.getItemIdTo());
          assertOptionEquals(now, batch.getIgnoredAt());
          assertOptionEquals("ignored", batch.getMessage());
        }
        {
          BatchQueueBatch batch = all.get(2);
          assertEquals(item3, batch.getItemIdFrom());
          assertEquals(item3, batch.getItemIdTo());
        }
        {
          BatchQueueBatch batch = all.get(3);
          assertEquals(item4, batch.getItemIdFrom());
          assertEquals(item5, batch.getItemIdTo());
        }
      }
    });
  }

  @Test
  public void process_success2() {
    Id<BatchQueueItem> item1 = createItem()
        .withQueueId(getUserIdQueueId())
        .withPayload(Json.number(11))
        .buildAndPersist(transacter())
        .getId();
    Id<BatchQueueItem> item2 = createItem()
        .withQueueId(getUserIdQueueId())
        .withPayload(Json.number(22))
        .buildAndPersist(transacter())
        .getId();
    Id<BatchQueueItem> item3 = createItem()
        .withQueueId(getUserIdQueueId())
        .withPayload(Json.number(33))
        .buildAndPersist(transacter())
        .getId();
    Id<BatchQueueItem> item4 = createItem()
        .withQueueId(getUserIdQueueId())
        .withPayload(Json.number(44))
        .buildAndPersist(transacter())
        .getId();
    Id<BatchQueueItem> item5 = createItem()
        .withQueueId(getUserIdQueueId())
        .withPayload(Json.number(55))
        .buildAndPersist(transacter())
        .getId();

    BatchQueueBatch original = createBatch()
        .withQueueId(getUserIdQueueId())
        .withItemIdFrom(item1)
        .withItemIdTo(item5)
        .withIgnoredAt(now, "ignored")
        .buildAndPersist(transacter());
    BatchQueueLockAndExecuteResult result =
        prepareQuery(new BatchQueueSeparateItemsByValue("UserIdQueue", original.getId(),
            ImmutableSet.of(Json.number(33), Json.number(44), Json.number(55)))).process();
    assertEquals(ImmutableSet.of(original.getId()), result.getProcessedIds());

    transacter().execute(new WithReadOnlySession() {
      @Inject BatchQueueBatchRepository repository;
      @Override
      public void run(DbSession session) {
        List<BatchQueueBatch> all = repository.getAll();
        assertEquals(2, all.size());
        {
          BatchQueueBatch batch = all.get(0);
          assertEquals(original.getId(), batch.getId());
          assertEquals(item1, batch.getItemIdFrom());
          assertEquals(item2, batch.getItemIdTo());
        }
        {
          BatchQueueBatch batch = all.get(1);
          assertEquals(item3, batch.getItemIdFrom());
          assertEquals(item5, batch.getItemIdTo());
          assertOptionEquals(now, batch.getIgnoredAt());
          assertOptionEquals("ignored", batch.getMessage());
        }
        assertOptionEquals(String.format("Resulting batches: {%s=[%s..%s], %s=[%s..%s]}", original.getId(),
            item1, item2, all.get(1).getId(), item3, item5), result.getMessage());
      }
    });
  }

  @Test
  public void process_success_duplicateValues() {
    Id<BatchQueueItem> item1 = createItem()
        .withQueueId(getUserIdQueueId())
        .withPayload(Json.number(11))
        .buildAndPersist(transacter())
        .getId();
    Id<BatchQueueItem> item2 = createItem()
        .withQueueId(getUserIdQueueId())
        .withPayload(Json.number(22))
        .buildAndPersist(transacter())
        .getId();
    Id<BatchQueueItem> item3 = createItem()
        .withQueueId(getUserIdQueueId())
        .withPayload(Json.number(22))
        .buildAndPersist(transacter())
        .getId();
    Id<BatchQueueItem> item4 = createItem()
        .withQueueId(getUserIdQueueId())
        .withPayload(Json.number(44))
        .buildAndPersist(transacter())
        .getId();
    Id<BatchQueueItem> item5 = createItem()
        .withQueueId(getUserIdQueueId())
        .withPayload(Json.number(55))
        .buildAndPersist(transacter())
        .getId();

    BatchQueueBatch original = createBatch()
        .withQueueId(getUserIdQueueId())
        .withItemIdFrom(item1)
        .withItemIdTo(item5)
        .withIgnoredAt(now, "ignored")
        .buildAndPersist(transacter());
    BatchQueueLockAndExecuteResult result =
        prepareQuery(new BatchQueueSeparateItemsByValue("UserIdQueue", original.getId(),
            ImmutableSet.of(Json.number(11), Json.number(22)))).process();
    assertEquals(ImmutableSet.of(original.getId()), result.getProcessedIds());

    transacter().execute(new WithReadOnlySession() {
      @Inject BatchQueueBatchRepository repository;
      @Override
      public void run(DbSession session) {
        List<BatchQueueBatch> all = repository.getAll();
        assertEquals(2, all.size());
        {
          BatchQueueBatch batch = all.get(0);
          assertEquals(original.getId(), batch.getId());
          assertEquals(item1, batch.getItemIdFrom());
          assertEquals(item3, batch.getItemIdTo());
        }
        {
          BatchQueueBatch batch = all.get(1);
          assertEquals(item4, batch.getItemIdFrom());
          assertEquals(item5, batch.getItemIdTo());
        }
      }
    });
  }

  @Test
  public void process_withNonIgnoredBatch_throws() {
    Id<BatchQueueItem> item1 = createItem()
        .withQueueId(getUserIdQueueId())
        .withPayload(Json.number(11))
        .buildAndPersist(transacter())
        .getId();
    Id<BatchQueueItem> item2 = createItem()
        .withQueueId(getUserIdQueueId())
        .withPayload(Json.number(22))
        .buildAndPersist(transacter())
        .getId();
    BatchQueueBatch batch = createBatch()
        .withQueueId(getUserIdQueueId())
        .withItemIdFrom(item1)
        .withItemIdTo(item2)
        .buildAndPersist(transacter());
    assertThrows(InvalidArgumentException.class, "Batch must be marked as ignored before it can be split",
        () -> prepareQuery(new BatchQueueSeparateItemsByValue("UserIdQueue", batch.getId(), ImmutableSet.of(Json.number(11)))).process());
  }

  @Test
  public void process_withWrongQueue_returnsMissing() {
    Id<BatchQueueItem> item1 = createItem()
        .withQueueId(getUserIdQueueId())
        .withPayload(Json.number(11))
        .buildAndPersist(transacter())
        .getId();
    Id<BatchQueueItem> item2 = createItem()
        .withQueueId(getUserIdQueueId())
        .withPayload(Json.number(22))
        .buildAndPersist(transacter())
        .getId();
    Id<BatchQueueName> otherQueueId = createName()
        .withQueueName("OtherQueue")
        .buildAndPersist(transacter())
        .getId();
    BatchQueueBatch batch = createBatch()
        .withItemIdFrom(item1)
        .withItemIdTo(item2)
        .withIgnoredAt(now, "ignored")
        .withQueueId(otherQueueId)
        .buildAndPersist(transacter());
    BatchQueueLockAndExecuteResult result =
        prepareQuery(new BatchQueueSeparateItemsByValue("UserIdQueue", batch.getId(), ImmutableSet.of(Json.number(11)))).process();
    assertEquals(ImmutableSet.of(batch.getId()), result.getNotFoundIds());
  }

  @Test
  public void process_itemIdsNotInBatch_throws() {
    Id<BatchQueueItem> item1 = createItem()
        .withQueueId(getUserIdQueueId())
        .withPayload(Json.number(11))
        .buildAndPersist(transacter())
        .getId();
    Id<BatchQueueItem> item2 = createItem()
        .withQueueId(getUserIdQueueId())
        .withPayload(Json.number(22))
        .buildAndPersist(transacter())
        .getId();
    BatchQueueBatch batch = createBatch()
        .withQueueId(getUserIdQueueId())
        .withItemIdFrom(item1)
        .withItemIdTo(item2)
        .withIgnoredAt(now, "ignored")
        .buildAndPersist(transacter());
    assertThrows(InvalidArgumentException.class, "Item 33 is not in batch " + batch.getId(),
        () -> prepareQuery(new BatchQueueSeparateItemsByValue("UserIdQueue", batch.getId(), ImmutableSet.of(Json.number(33)))).process());
  }

  private BatchQueueSeparateItemsByValue prepareQuery(BatchQueueSeparateItemsByValue query) {
    query.binder = injector().getInstance(BatchQueueBinder.class);
    query.now = now;
    return query;
  }
  
}