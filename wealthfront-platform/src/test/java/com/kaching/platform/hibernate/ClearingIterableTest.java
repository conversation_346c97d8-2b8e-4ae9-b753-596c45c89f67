package com.kaching.platform.hibernate;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import java.util.Iterator;
import java.util.concurrent.atomic.AtomicBoolean;

import org.junit.Test;

import com.kaching.util.GenericLists;

public class ClearingIterableTest {

  @Test
  public void testEvictsEntries() {
    final AtomicBoolean cleared = new AtomicBoolean(false);

    DbSession session = new AbstractDbSession() {
      @Override
      public void clear() {
        cleared.set(true);
      }
    };

    Iterator<Integer> iterator = new ClearingIterable<>(session, GenericLists.list(1, 2).iterator()).iterator();
    assertTrue(iterator.hasNext());
    assertEquals(1, (int) iterator.next());
    assertTrue(cleared.getAndSet(false));

    assertTrue(iterator.hasNext());
    assertEquals(2, (int) iterator.next());
    assertTrue(cleared.getAndSet(false));

    assertFalse(iterator.hasNext());
    assertTrue(cleared.getAndSet(false));
  }

}
