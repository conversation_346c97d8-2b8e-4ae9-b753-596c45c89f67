package com.kaching.platform.memcached;

import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.nullValue;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThat;

import java.net.InetSocketAddress;
import java.net.SocketAddress;
import java.util.Map;

import org.junit.Test;

import com.google.common.collect.ImmutableMap;
import com.kaching.platform.testing.Mockeries;
import com.kaching.platform.testing.Mockeries.WFMockery;
import com.kaching.platform.testing.WExpectations;

public class MemcachedStatsProviderImplTest {

  private final WFMockery mockery = Mockeries.mockery();
  private final MemcachedClient client = mockery.mock(MemcachedClient.class);
  private final MemcachedUtilizationStats utilizationStats = new MemcachedUtilizationStats();

  @Test
  public void getStats() {
    InetSocketAddress address = new InetSocketAddress("mem0", 11211);
    Map<String, String> rawStats = ImmutableMap.<String, String>builder()
        .put("bytes", "1")
        .put("limit_maxbytes", "2")
        .put("total_items", "3")
        .put("curr_items", "2")
        .put("evictions", "4")
        .put("reclaimed", "5")
        .put("curr_connections", "6")
        .put("cmd_get", "7")
        .put("cmd_set", "8")
        .put("get_hits", "9")
        .put("get_misses", "10")
        .put("incr_hits", "11")
        .put("incr_misses", "12")
        .put("delete_hits", "13")
        .put("delete_misses", "14")
        .build();

    mockery.checking(new WExpectations() {{
      oneOf(client).getStats();
      will(returnValue(ImmutableMap.of(address, rawStats)));
    }});

    Map<SocketAddress, MemcachedStats> stats = getProvider().getStats();

    assertEquals(1, stats.size());
    MemcachedStats addressStats = stats.get(address);
    assertEquals(1, addressStats.getBytes());
    assertEquals(2, addressStats.getMaxBytes());
    assertEquals(3, addressStats.getTotalItems());
    assertEquals(4, addressStats.getEvictions());
    assertEquals(5, addressStats.getReclaimed());
    assertEquals(6, addressStats.getCurrentConnections());
    assertEquals(7, addressStats.getGetCmds());
    assertEquals(8, addressStats.getSetCmds());
    assertEquals(9, addressStats.getGetHits());
    assertEquals(10, addressStats.getGetMisses());
    assertEquals(11, addressStats.getIncrHits());
    assertEquals(12, addressStats.getIncrMisses());
    assertEquals(13, addressStats.getDeleteHits());
    assertEquals(14, addressStats.getDeleteMisses());

    assertThat(utilizationStats.getAppMem0Bytes(), is(1L));
    assertThat(utilizationStats.getAppMem0CurrItems(), is(2L));
    assertThat(utilizationStats.getAppMem0LimitMaxBytes(), is(2L));
    assertThat(utilizationStats.getAppMem1CurrItems(), is(nullValue()));
    assertThat(utilizationStats.getAppMem1CurrItems(), is(nullValue()));
    assertThat(utilizationStats.getAppMem1LimitMaxBytes(), is(nullValue()));
  }

  private MemcachedStatsProviderImpl getProvider() {
    MemcachedStatsProviderImpl provider = new MemcachedStatsProviderImpl();
    provider.client = client;
    provider.utilizationStats = utilizationStats;
    return provider;
  }

}