package com.kaching.platform.memcached;

import static com.wealthfront.test.Assert.assertEmpty;
import static com.wealthfront.test.Assert.assertThrows;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import java.net.URI;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

import org.joda.time.DateTime;
import org.junit.After;
import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Iterables;
import com.google.inject.Provider;
import com.google.inject.util.Providers;
import com.wealthfront.util.time.DateTimeZones;

import net.spy.memcached.transcoders.LongTranscoder;

public class TestMemcachedClientTest {

  private static final DateTime NOW = new DateTime(2018, 1, 1, 0, 0, DateTimeZones.ET);
  private static final Provider<DateTime> clock = Providers.of(NOW);
  private static final TestMemcachedClient testMemcachedClient = new TestMemcachedClient(clock, ImmutableSet.of(
      URI.create("http://www.wealthfront.com")
  ));

  @After
  public void after() {
    testMemcachedClient.flush();
  }

  @Test
  public void getAddresses() {
    assertEquals("http://www.wealthfront.com", Iterables.getOnlyElement(testMemcachedClient.getAddresses()).toString());
  }

  @Test
  public void add() throws ExecutionException, InterruptedException {
    Future<Boolean> response = testMemcachedClient.add("key", 1, "value");
    assertTrue(response.isDone());
    assertTrue(response.get());
    assertNull(testMemcachedClient.get("key"));

  }

  @Test
  public void add_withTranscoder() throws ExecutionException, InterruptedException {
    Future<Boolean> response = testMemcachedClient.add("key", 1, "value", null);
    assertTrue(response.isDone());
    assertTrue(response.get());
    assertNull(testMemcachedClient.get("key"));
  }

  @Test
  public void append() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.append(0, "key", null));
  }

  @Test
  public void append_withTranscoder() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.append(0, "key", null, null));
  }

  @Test
  public void asyncCAS() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.asyncCAS("key", 0, null));
  }

  @Test
  public void asyncCAS_withTranscoder() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.asyncCAS("key", 0, null, null));
  }

  @Test
  public void asyncGet_withTranscoder() throws ExecutionException, InterruptedException {
    Future<Boolean> result1 = testMemcachedClient.asyncGet("key", null);
    assertTrue(result1.isDone());
    assertNull(result1.get());

    testMemcachedClient.add("key", 1, "value");
    Future<Boolean> result2 = testMemcachedClient.asyncGet("key", null);
    assertTrue(result2.isDone());
    assertNull(result2.get());
  }

  @Test
  public void asyncGet() throws ExecutionException, InterruptedException {
    Future<Object> result1 = testMemcachedClient.asyncGet("key");
    assertTrue(result1.isDone());
    assertNull(result1.get());

    testMemcachedClient.add("key", 1, "value");
    Future<Object> result2 = testMemcachedClient.asyncGet("key");
    assertTrue(result2.isDone());
    assertNull(result2.get());
  }

  @Test
  public void asyncGetBulk_withTranscoder() throws ExecutionException, InterruptedException {
    Future<Map<String, Long>> result1 = testMemcachedClient.asyncGetBulk(
        ImmutableList.of("key1", "key2"), new LongTranscoder());
    assertTrue(result1.isDone());
    assertEquals(0, result1.get().size());

    testMemcachedClient.add("key1", 1, 4);
    testMemcachedClient.add("key2", 1, 5);
    Future<Map<String, Long>> result2 = testMemcachedClient.asyncGetBulk(
        ImmutableList.of("key1", "key2"), new LongTranscoder());
    assertTrue(result2.isDone());
    assertEquals(ImmutableMap.of(), result2.get());
  }

  @Test
  public void asyncGetBulk() throws ExecutionException, InterruptedException {
    Future<Map<String, Object>> result1 = testMemcachedClient.asyncGetBulk(ImmutableList.of("key1", "key2"));
    assertTrue(result1.isDone());
    assertEquals(0, result1.get().size());

    testMemcachedClient.add("key1", 1, "value1");
    testMemcachedClient.add("key2", 1, "value2");
    Future<Map<String, Object>> result2 = testMemcachedClient.asyncGetBulk(ImmutableList.of("key1", "key2"));
    assertTrue(result2.isDone());
    assertEquals(ImmutableMap.of(), result2.get());
  }

  @Test
  public void asyncGetBulk_stringArray() throws ExecutionException, InterruptedException {
    Future<Map<String, Object>> result1 = testMemcachedClient.asyncGetBulk(new String[] {"key1", "key2"});
    assertTrue(result1.isDone());
    assertEquals(0, result1.get().size());

    testMemcachedClient.add("key1", 1, "value1");
    testMemcachedClient.add("key2", 1, "value2");
    Future<Map<String, Object>> result2 = testMemcachedClient.asyncGetBulk(new String[] {"key1", "key2"});
    assertTrue(result2.isDone());
    assertEquals(ImmutableMap.of(), result2.get());
  }

  @Test
  public void asyncGetBulk_stringArrayWithTranscoder() throws ExecutionException, InterruptedException {
    Future<Map<String, Object>> result1 = testMemcachedClient.asyncGetBulk(null, new String[] {"key1", "key2"});
    assertTrue(result1.isDone());
    assertEquals(0, result1.get().size());

    testMemcachedClient.add("key1", 1, "value1");
    testMemcachedClient.add("key2", 1, "value2");
    Future<Map<String, Object>> result2 = testMemcachedClient.asyncGetBulk(null, new String[] {"key1", "key2"});
    assertTrue(result2.isDone());
    assertEquals(ImmutableMap.of(), result2.get());
  }

  @Test
  public void asyncGets_withTranscoder() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.asyncGets("key", null));
  }

  @Test
  public void asyncGets() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.asyncGets("key"));
  }

  @Test
  public void cas() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.cas("key", 0, null));
  }

  @Test
  public void cas_withTranscoder() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.cas("key", 0, null, null));
  }

  @Test
  public void checkAccess() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.checkAccess());
  }

  @Test
  public void decr_withDefault() {
    assertEquals(-1, testMemcachedClient.decr("key", 0, 0));
  }

  @Test
  public void decr() {
    assertEquals(-1, testMemcachedClient.decr("key", 0));

    testMemcachedClient.add("key", 1, 5);
    assertEquals(-1, testMemcachedClient.decr("key", 3));
  }

  @Test
  public void delete() throws ExecutionException, InterruptedException {
    Future<Boolean> result = testMemcachedClient.delete("key");
    assertTrue(result.isDone());
    assertTrue(result.get());
  }

  @Test
  public void flush() throws ExecutionException, InterruptedException {
    Future<Boolean> result = testMemcachedClient.flush();
    assertTrue(result.isDone());
    assertTrue(result.get());
  }

  @Test
  public void flush_withDelay() throws ExecutionException, InterruptedException {
    Future<Boolean> result = testMemcachedClient.flush(1);
    assertTrue(result.isDone());
    assertTrue(result.get());
  }

  @Test
  public void get_withTranscoder() {
    assertNull(testMemcachedClient.get("key", null));

    testMemcachedClient.add("key", 1, "value");
    assertNull(testMemcachedClient.get("key", null));
  }

  @Test
  public void get() {
    assertNull(testMemcachedClient.get("key1"));

    testMemcachedClient.add("key1", 1, "value");
    assertNull(testMemcachedClient.get("key1"));

    testMemcachedClient.add("key2", -1, "value");
    assertNull(testMemcachedClient.get("key2"));
  }

  @Test
  public void getAvailableServers() {
    assertEmpty(testMemcachedClient.getAvailableServers());
  }

  @Test
  public void getBulk_collectionTranscoder() {
    assertEquals(0, testMemcachedClient.getBulk(ImmutableList.of("key1", "key2"), null).size());

    testMemcachedClient.add("key1", 1, "value1");
    testMemcachedClient.add("key2", 1, "value2");
    assertEquals(ImmutableMap.of(), testMemcachedClient.getBulk(ImmutableList.of("key1", "key2"), null));
  }

  @Test
  public void getBulk_collection() {
    assertEquals(0, testMemcachedClient.getBulk(ImmutableList.of("key1", "key2")).size());

    testMemcachedClient.add("key1", 1, "value1");
    testMemcachedClient.add("key2", 1, "value2");
    assertEquals(ImmutableMap.of(), testMemcachedClient.getBulk(ImmutableList.of("key1", "key2")));
  }

  @Test
  public void getBulk_stringArray() {
    assertEquals(0, testMemcachedClient.getBulk(new String[] {"key1", "key2"}).size());

    testMemcachedClient.add("key1", 1, "value1");
    testMemcachedClient.add("key2", 1, "value2");
    assertEquals(ImmutableMap.of(), testMemcachedClient.getBulk(new String[] {"key1", "key2"}));
  }

  @Test
  public void getBulk_transcoderStringArray() {
    assertEquals(0, testMemcachedClient.getBulk(null, new String[] {"key1", "key2"}).size());

    testMemcachedClient.add("key1", 1, "value1");
    testMemcachedClient.add("key2", 1, "value2");
    assertEquals(ImmutableMap.of(), testMemcachedClient.getBulk(null, new String[] {"key1", "key2"}));
  }

  @Test
  public void getContextClassLoader() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.getContextClassLoader());
  }

  @Test
  public void getId() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.getId());
  }

  @Test
  public void getName() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.getName());
  }

  @Test
  public void getPriority() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.getPriority());
  }

  @Test
  public void gets_transcoder() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.gets("key", null));
  }

  @Test
  public void gets() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.gets("key"));
  }

  @Test
  public void getState() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.getState());
  }

  @Test
  public void getStats() {
    assertEquals(0, testMemcachedClient.getStats().size());
  }

  @Test
  public void getTranscoder() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.getTranscoder());
  }

  @Test
  public void getUnavailableServers() {
    assertEmpty(testMemcachedClient.getUnavailableServers());
  }

  @Test
  public void getUncaughtExceptionHandler() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.getUncaughtExceptionHandler());
  }

  @Test
  public void getVersions() {
    assertEquals(0, testMemcachedClient.getVersions().size());
  }

  @Test
  public void incr_withExp() {
    assertEquals(5, testMemcachedClient.incr("key", 1, 5, -1));
    assertNull(testMemcachedClient.get("key"));

    testMemcachedClient.add("key", 1, 5);
    assertEquals(5, testMemcachedClient.incr("key", 1, 5, 0));
  }

  @Test
  public void incr_withDefault() {
    assertEquals(5, testMemcachedClient.incr("key", 1, 5));
    assertEquals(5, testMemcachedClient.incr("key", 1, 5));
  }

  @Test
  public void incr() {
    assertEquals(-1, testMemcachedClient.incr("key", 5));

    testMemcachedClient.add("key", 1, 5);
    assertEquals(-1, testMemcachedClient.incr("key", 1));
  }

  @Test
  public void isAlive() {
    assertTrue(testMemcachedClient.isAlive());
  }

  @Test
  public void isDaemon() {
    assertFalse(testMemcachedClient.isDaemon());
  }

  @Test
  public void isInterrupted() {
    assertFalse(testMemcachedClient.isInterrupted());
  }

  @Test
  public void prepend() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.prepend(0, "key", null));
  }

  @Test
  public void prepend_withTranscoder() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.prepend(0, "key", null, null));
  }

  @Test
  public void replace() throws ExecutionException, InterruptedException {
    testMemcachedClient.add("key", 1, "value");

    Future<Boolean> result = testMemcachedClient.replace("key", 0, "new value");
    assertTrue(result.isDone());
    assertTrue(result.get());
    assertNull(testMemcachedClient.get("key"));
  }

  @Test
  public void replace_withTranscoder() throws ExecutionException, InterruptedException {
    testMemcachedClient.add("key", 1, "value");

    Future<Boolean> result = testMemcachedClient.replace("key", 0, "new value", null);
    assertTrue(result.isDone());
    assertTrue(result.get());
    assertNull(testMemcachedClient.get("key"));
  }

  @Test
  public void run() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.run());
  }

  @Test
  public void set() throws ExecutionException, InterruptedException {
    Future<Boolean> result = testMemcachedClient.set("key", 0, "value");
    assertTrue(result.isDone());
    assertTrue(result.get());
    assertNull(testMemcachedClient.get("key"));
  }

  @Test
  public void set_withTranscoder() throws ExecutionException, InterruptedException {
    Future<Boolean> result = testMemcachedClient.set("key", 0, "value", null);
    assertTrue(result.isDone());
    assertTrue(result.get());
    assertNull(testMemcachedClient.get("key"));
  }

  @Test
  public void setContextClassLoader() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.setContextClassLoader(null));
  }

  @Test
  public void setDaemon() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.setDaemon(true));
  }

  @Test
  public void setName() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.setName("key"));
  }

  @Test
  public void setPriority() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.setPriority(5));
  }

  @Test
  public void setUncaughtExceptionHandler() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.setUncaughtExceptionHandler(null));
  }

  @Test
  public void shutdown() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.shutdown());
  }

  @Test
  public void shutdown_withTimeout() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.shutdown(0, null));
  }

  @Test
  public void start() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.start());
  }

  @Test
  public void waitForQueues() {
    assertThrows(UnsupportedOperationException.class, () -> testMemcachedClient.waitForQueues(0, null));
  }
}