package com.kaching.platform.memcached;

import static com.wealthfront.test.Assert.assertEmpty;
import static com.wealthfront.test.Assert.assertThrows;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import java.net.URI;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

import org.joda.time.DateTime;
import org.junit.After;
import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Iterables;
import com.google.inject.Provider;
import com.google.inject.util.Providers;
import com.wealthfront.util.time.DateTimeZones;

import net.spy.memcached.transcoders.LongTranscoder;

public class FakeMemcachedClientTest {

  private static final DateTime NOW = new DateTime(2018, 1, 1, 0, 0, DateTimeZones.ET);
  private static final Provider<DateTime> clock = Providers.of(NOW);
  private static final FakeMemcachedClient fakeMemcachedClient = new FakeMemcachedClient(clock, ImmutableSet.of(
      URI.create("http://www.wealthfront.com")
  ));

  @After
  public void after() {
    fakeMemcachedClient.flush();
  }

  @Test
  public void getAddresses() {
    assertEquals("http://www.wealthfront.com", Iterables.getOnlyElement(fakeMemcachedClient.getAddresses()).toString());
  }

  @Test
  public void add() throws ExecutionException, InterruptedException {
    Future<Boolean> response = fakeMemcachedClient.add("key", 1, "value");
    assertTrue(response.isDone());
    assertTrue(response.get());
    assertEquals("value", fakeMemcachedClient.get("key"));

    Future<Boolean> newResponse = fakeMemcachedClient.add("key", 1, "value");
    assertTrue(newResponse.isDone());
    assertFalse(newResponse.get());
    assertEquals("value", fakeMemcachedClient.get("key"));
  }

  @Test
  public void add_withTranscoder() throws ExecutionException, InterruptedException {
    Future<Boolean> response = fakeMemcachedClient.add("key", 1, "value", null);
    assertTrue(response.isDone());
    assertTrue(response.get());
    assertEquals("value", fakeMemcachedClient.get("key"));
  }

  @Test
  public void append() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.append(0, "key", null));
  }

  @Test
  public void append_withTranscoder() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.append(0, "key", null, null));
  }

  @Test
  public void asyncCAS() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.asyncCAS("key", 0, null));
  }

  @Test
  public void asyncCAS_withTranscoder() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.asyncCAS("key", 0, null, null));
  }

  @Test
  public void asyncGet_withTranscoder() throws ExecutionException, InterruptedException {
    Future<Boolean> result1 = fakeMemcachedClient.asyncGet("key", null);
    assertTrue(result1.isDone());
    assertNull(result1.get());

    fakeMemcachedClient.add("key", 1, "value");
    Future<String> result2 = fakeMemcachedClient.asyncGet("key", null);
    assertTrue(result2.isDone());
    assertEquals("value", result2.get());
  }

  @Test
  public void asyncGet() throws ExecutionException, InterruptedException {
    Future<Object> result1 = fakeMemcachedClient.asyncGet("key");
    assertTrue(result1.isDone());
    assertNull(result1.get());

    fakeMemcachedClient.add("key", 1, "value");
    Future<Object> result2 = fakeMemcachedClient.asyncGet("key");
    assertTrue(result2.isDone());
    assertEquals("value", result2.get());
  }

  @Test
  public void asyncGetBulk_withTranscoder() throws ExecutionException, InterruptedException {
    Future<Map<String, Long>> result1 = fakeMemcachedClient.asyncGetBulk(
        ImmutableList.of("key1", "key2"), new LongTranscoder());
    assertTrue(result1.isDone());
    assertEquals(0, result1.get().size());

    fakeMemcachedClient.add("key1", 1, 4L);
    fakeMemcachedClient.add("key2", 1, 5L);
    Future<Map<String, Long>> result2 = fakeMemcachedClient.asyncGetBulk(
        ImmutableList.of("key1", "key2"), new LongTranscoder());
    assertTrue(result2.isDone());
    assertEquals(ImmutableMap.of("key1", 4L, "key2", 5L), result2.get());
  }

  @Test
  public void asyncGetBulk() throws ExecutionException, InterruptedException {
    Future<Map<String, Object>> result1 = fakeMemcachedClient.asyncGetBulk(ImmutableList.of("key1", "key2"));
    assertTrue(result1.isDone());
    assertEquals(0, result1.get().size());

    fakeMemcachedClient.add("key1", 1, "value1");
    fakeMemcachedClient.add("key2", 1, "value2");
    Future<Map<String, Object>> result2 = fakeMemcachedClient.asyncGetBulk(ImmutableList.of("key1", "key2"));
    assertTrue(result2.isDone());
    assertEquals(ImmutableMap.of("key1", "value1", "key2", "value2"), result2.get());
  }

  @Test
  public void asyncGetBulk_stringArray() throws ExecutionException, InterruptedException {
    Future<Map<String, Object>> result1 = fakeMemcachedClient.asyncGetBulk(new String[]{"key1", "key2"});
    assertTrue(result1.isDone());
    assertEquals(0, result1.get().size());

    fakeMemcachedClient.add("key1", 1, "value1");
    fakeMemcachedClient.add("key2", 1, "value2");
    Future<Map<String, Object>> result2 = fakeMemcachedClient.asyncGetBulk(new String[]{"key1", "key2"});
    assertTrue(result2.isDone());
    assertEquals(ImmutableMap.of("key1", "value1", "key2", "value2"), result2.get());
  }

  @Test
  public void asyncGetBulk_stringArrayWithTranscoder() throws ExecutionException, InterruptedException {
    Future<Map<String, Object>> result1 = fakeMemcachedClient.asyncGetBulk(null, new String[]{"key1", "key2"});
    assertTrue(result1.isDone());
    assertEquals(0, result1.get().size());

    fakeMemcachedClient.add("key1", 1, "value1");
    fakeMemcachedClient.add("key2", 1, "value2");
    Future<Map<String, Object>> result2 = fakeMemcachedClient.asyncGetBulk(null, new String[]{"key1", "key2"});
    assertTrue(result2.isDone());
    assertEquals(ImmutableMap.of("key1", "value1", "key2", "value2"), result2.get());
  }

  @Test
  public void asyncGets_withTranscoder() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.asyncGets("key", null));
  }

  @Test
  public void asyncGets() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.asyncGets("key"));
  }

  @Test
  public void cas() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.cas("key", 0, null));
  }

  @Test
  public void cas_withTranscoder() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.cas("key", 0, null, null));
  }

  @Test
  public void checkAccess() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.checkAccess());
  }

  @Test
  public void decr_withDefault() {
    assertEquals(-1, fakeMemcachedClient.decr("key", 0, 0));
  }

  @Test
  public void decr() {
    assertEquals(-1, fakeMemcachedClient.decr("key", 0));

    fakeMemcachedClient.add("key", 1, 5);
    assertEquals(2, fakeMemcachedClient.decr("key", 3));
  }

  @Test
  public void delete() throws ExecutionException, InterruptedException {
    Future<Boolean> result = fakeMemcachedClient.delete("key");
    assertTrue(result.isDone());
    assertTrue(result.get());
  }

  @Test
  public void flush() throws ExecutionException, InterruptedException {
    Future<Boolean> result = fakeMemcachedClient.flush();
    assertTrue(result.isDone());
    assertTrue(result.get());
  }

  @Test
  public void flush_withDelay() throws ExecutionException, InterruptedException {
    Future<Boolean> result = fakeMemcachedClient.flush(1);
    assertTrue(result.isDone());
    assertTrue(result.get());
  }

  @Test
  public void get_withTranscoder() {
    assertNull(fakeMemcachedClient.get("key", null));

    fakeMemcachedClient.add("key", 1, "value");
    assertEquals("value", fakeMemcachedClient.get("key", null));
  }

  @Test
  public void get() {
    assertNull(fakeMemcachedClient.get("key1"));

    fakeMemcachedClient.add("key1", 1, "value");
    assertEquals("value", fakeMemcachedClient.get("key1"));

    fakeMemcachedClient.add("key2", -1, "value");
    assertNull(fakeMemcachedClient.get("key2"));
  }

  @Test
  public void getAvailableServers() {
    assertEmpty(fakeMemcachedClient.getAvailableServers());
  }

  @Test
  public void getBulk_collectionTranscoder() {
    assertEquals(0, fakeMemcachedClient.getBulk(ImmutableList.of("key1", "key2"), null).size());

    fakeMemcachedClient.add("key1", 1, "value1");
    fakeMemcachedClient.add("key2", 1, "value2");
    assertEquals(ImmutableMap.of("key1", "value1", "key2", "value2"),
        fakeMemcachedClient.getBulk(ImmutableList.of("key1", "key2"), null));
  }

  @Test
  public void getBulk_collection() {
    assertEquals(0, fakeMemcachedClient.getBulk(ImmutableList.of("key1", "key2")).size());

    fakeMemcachedClient.add("key1", 1, "value1");
    fakeMemcachedClient.add("key2", 1, "value2");
    assertEquals(ImmutableMap.of("key1", "value1", "key2", "value2"),
        fakeMemcachedClient.getBulk(ImmutableList.of("key1", "key2")));
  }

  @Test
  public void getBulk_stringArray() {
    assertEquals(0, fakeMemcachedClient.getBulk(new String[]{"key1", "key2"}).size());

    fakeMemcachedClient.add("key1", 1, "value1");
    fakeMemcachedClient.add("key2", 1, "value2");
    assertEquals(ImmutableMap.of("key1", "value1", "key2", "value2"),
        fakeMemcachedClient.getBulk(new String[]{"key1", "key2"}));
  }

  @Test
  public void getBulk_transcoderStringArray() {
    assertEquals(0, fakeMemcachedClient.getBulk(null, new String[]{"key1", "key2"}).size());

    fakeMemcachedClient.add("key1", 1, "value1");
    fakeMemcachedClient.add("key2", 1, "value2");
    assertEquals(ImmutableMap.of("key1", "value1", "key2", "value2"),
        fakeMemcachedClient.getBulk(null, new String[]{"key1", "key2"}));
  }

  @Test
  public void getContextClassLoader() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.getContextClassLoader());
  }

  @Test
  public void getId() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.getId());
  }

  @Test
  public void getName() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.getName());
  }

  @Test
  public void getPriority() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.getPriority());
  }

  @Test
  public void gets_transcoder() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.gets("key", null));
  }

  @Test
  public void gets() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.gets("key"));
  }

  @Test
  public void getState() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.getState());
  }

  @Test
  public void getStats() {
    assertEquals(0, fakeMemcachedClient.getStats().size());
  }

  @Test
  public void getTranscoder() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.getTranscoder());
  }

  @Test
  public void getUnavailableServers() {
    assertEmpty(fakeMemcachedClient.getUnavailableServers());
  }

  @Test
  public void getUncaughtExceptionHandler() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.getUncaughtExceptionHandler());
  }

  @Test
  public void getVersions() {
    assertEquals(0, fakeMemcachedClient.getVersions().size());
  }

  @Test
  public void incr_withExp() {
    assertEquals(5, fakeMemcachedClient.incr("key", 1, 5, -1));
    assertNull(fakeMemcachedClient.get("key"));

    fakeMemcachedClient.add("key", 1, 5);
    assertEquals(6, fakeMemcachedClient.incr("key", 1, 5, 0));
  }

  @Test
  public void incr_withDefault() {
    assertEquals(5, fakeMemcachedClient.incr("key", 1, 5));
    assertEquals(6, fakeMemcachedClient.incr("key", 1, 5));
  }

  @Test
  public void incr() {
    assertEquals(-1, fakeMemcachedClient.incr("key", 5));

    fakeMemcachedClient.add("key", 1, 5);
    assertEquals(6, fakeMemcachedClient.incr("key", 1));
  }

  @Test
  public void incr_multiThread() throws InterruptedException {
    int numThreads = 3;
    fakeMemcachedClient.incr("key", 1, 0);
    ExecutorService executor = Executors.newFixedThreadPool(numThreads);
    CountDownLatch startLatch = new CountDownLatch(1);
    CountDownLatch endLatch = new CountDownLatch(numThreads);
    for (int i = 0; i < numThreads; i++) {
      executor.submit(() -> {
        try {
          startLatch.await();
          fakeMemcachedClient.incr("key", 1);
        } catch (InterruptedException e) {
          Thread.currentThread().interrupt();
        } finally {
          endLatch.countDown();
        }
      });
    }

    startLatch.countDown();
    endLatch.await(10, TimeUnit.SECONDS);
    executor.shutdown();

    assertEquals(numThreads, fakeMemcachedClient.incr("key", 0));

  }

  @Test
  public void isAlive() {
    assertTrue(fakeMemcachedClient.isAlive());
  }

  @Test
  public void isDaemon() {
    assertFalse(fakeMemcachedClient.isDaemon());
  }

  @Test
  public void isInterrupted() {
    assertFalse(fakeMemcachedClient.isInterrupted());
  }

  @Test
  public void prepend() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.prepend(0, "key", null));
  }

  @Test
  public void prepend_withTranscoder() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.prepend(0, "key", null, null));
  }

  @Test
  public void replace() throws ExecutionException, InterruptedException {
    fakeMemcachedClient.add("key", 1, "value");

    Future<Boolean> result = fakeMemcachedClient.replace("key", 0, "new value");
    assertTrue(result.isDone());
    assertTrue(result.get());
    assertEquals("new value", fakeMemcachedClient.get("key"));
  }

  @Test
  public void replace_withTranscoder() throws ExecutionException, InterruptedException {
    fakeMemcachedClient.add("key", 1, "value");

    Future<Boolean> result = fakeMemcachedClient.replace("key", 0, "new value", null);
    assertTrue(result.isDone());
    assertTrue(result.get());
    assertEquals("new value", fakeMemcachedClient.get("key"));
  }

  @Test
  public void run() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.run());
  }

  @Test
  public void set() throws ExecutionException, InterruptedException {
    Future<Boolean> result = fakeMemcachedClient.set("key", 0, "value");
    assertTrue(result.isDone());
    assertTrue(result.get());
    assertEquals("value", fakeMemcachedClient.get("key"));
  }

  @Test
  public void set_withTranscoder() throws ExecutionException, InterruptedException {
    Future<Boolean> result = fakeMemcachedClient.set("key", 0, "value", null);
    assertTrue(result.isDone());
    assertTrue(result.get());
    assertEquals("value", fakeMemcachedClient.get("key"));
  }

  @Test
  public void setContextClassLoader() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.setContextClassLoader(null));
  }

  @Test
  public void setDaemon() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.setDaemon(true));
  }

  @Test
  public void setName() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.setName("key"));
  }

  @Test
  public void setPriority() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.setPriority(5));
  }

  @Test
  public void setUncaughtExceptionHandler() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.setUncaughtExceptionHandler(null));
  }

  @Test
  public void shutdown() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.shutdown());
  }

  @Test
  public void shutdown_withTimeout() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.shutdown(0, null));
  }

  @Test
  public void start() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.start());
  }

  @Test
  public void waitForQueues() {
    assertThrows(UnsupportedOperationException.class, () -> fakeMemcachedClient.waitForQueues(0, null));
  }

}