package com.kaching.platform.testing;

import static com.kaching.platform.testing.QueryMatcher.query;

import java.util.Map;

import org.hamcrest.Description;
import org.junit.internal.matchers.TypeSafeMatcher;

import com.kaching.platform.queryengine.Query;

public class QueryMapMatcher<K, V extends Query<T>, T> extends TypeSafeMatcher<Map<K, V>> {

  private final Map<K, V> expected;

  QueryMapMatcher(Map<K, V> expected) {
    this.expected = expected;
  }

  @Override
  public boolean matchesSafely(Map<K, V> actual) {
    return expected.size() == actual.size() &&
        expected.entrySet().stream().allMatch(
            entry -> actual.containsKey(entry.getKey()) &&
                query(entry.getValue()).matchesSafely(actual.get(entry.getKey())));
  }

  @Override
  public void describeTo(Description description) {
    for (K key : expected.keySet()) {
      description.appendText("[K: ");
      description.appendValue(key);
      description.appendText(", V: ");
      query(expected.get(key)).describeTo(description);
      description.appendText("] ");
    }
  }

  public static <K, V extends Query<T>, T> QueryMapMatcher<K, V, T> queryMap(Map<K, V> expected) {
    return new QueryMapMatcher<>(expected);
  }

}
