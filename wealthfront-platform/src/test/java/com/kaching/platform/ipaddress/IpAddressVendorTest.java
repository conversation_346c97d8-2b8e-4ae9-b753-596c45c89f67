package com.kaching.platform.ipaddress;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotSame;
import static org.junit.Assert.assertSame;

import org.junit.Test;

public class IpAddressVendorTest {

  @Test
  public void ipAddressVendor() {
    assertSame(IpAddressVendor.YODLEE, IpAddressVendor.valueOf("YODLEE"));
    assertEquals("YODLEE", IpAddressVendor.YODLEE.name());

    assertSame(IpAddressVendor.EMONEY, IpAddressVendor.valueOf("EMONEY"));
    assertEquals("EMONEY", IpAddressVendor.EMONEY.name());

    assertNotSame(IpAddressVendor.YODLEE, IpAddressVendor.valueOf("EMONEY"));
    assertNotEquals("YODLEE", IpAddressVendor.EMONEY.name());
  }
  
  @Test
  public void visit() {
    int count = 0;
    for (IpAddressVendor vendor : IpAddressVendor.values()) {
      count += vendor.visit(new IpAddressVendorVisitor<Integer>() {

        @Override
        public Integer visitDebug(IpAddressVendor vendor) {
          assertSame(vendor, IpAddressVendor.DEBUG);
          return 1;
        }

        @Override
        public Integer visitEMoney(IpAddressVendor vendor) {
          assertSame(vendor, IpAddressVendor.EMONEY);
          return 1;
        }

        @Override
        public Integer visitFiserv(IpAddressVendor vendor) {
          assertSame(vendor, IpAddressVendor.FISERV);
          return 1;
        }

        @Override
        public Integer visitIntuit(IpAddressVendor vendor) {
          assertSame(vendor, IpAddressVendor.INTUIT);
          return 1;
        }

        @Override
        public Integer visitPlaid(IpAddressVendor vendor) {
          assertSame(vendor, IpAddressVendor.PLAID);
          return 1;
        }

        @Override
        public Integer visitQuovo(IpAddressVendor vendor) {
          assertSame(vendor, IpAddressVendor.QUOVO);
          return 1;
        }

        @Override
        public Integer visitMint(IpAddressVendor vendor) {
          assertSame(vendor, IpAddressVendor.MINT);
          return 1;
        }

        @Override
        public Integer visitMorningStar(IpAddressVendor vendor) {
          assertSame(vendor, IpAddressVendor.MORNINGSTAR);
          return 1;
        }

        @Override
        public Integer visitMX(IpAddressVendor vendor) {
          assertSame(vendor, IpAddressVendor.MX);
          return 1;
        }

        @Override
        public Integer visitPingdom(IpAddressVendor vendor) {
          assertSame(vendor, IpAddressVendor.PINGDOM);
          return 1;
        }

        @Override
        public Integer visitTurboTax(IpAddressVendor vendor) {
          assertSame(vendor, IpAddressVendor.TURBOTAX);
          return 1;
        }

        @Override
        public Integer visitYodlee(IpAddressVendor vendor) {
          assertSame(vendor, IpAddressVendor.YODLEE);
          return 1;
        }

        @Override
        public Integer visitFinicity(IpAddressVendor ipAddressVendor) {
          assertSame(vendor, IpAddressVendor.FINICITY);
          return 1;
        }

        @Override
        public Integer visitDataTheorem(IpAddressVendor vendor) {
          assertSame(vendor, IpAddressVendor.DATATHEOREM);
          return 1;
        }
      });
    }
    assertEquals(IpAddressVendor.values().length, count);
  }

}
