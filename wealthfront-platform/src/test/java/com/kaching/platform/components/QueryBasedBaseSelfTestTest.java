package com.kaching.platform.components;

import static com.google.common.collect.Lists.newArrayList;
import static com.kaching.platform.testing.Mockeries.mockery;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;

import java.util.Collection;
import java.util.Collections;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import com.google.common.collect.ImmutableList;
import com.google.inject.TypeLiteral;
import com.kaching.platform.common.Errors;
import com.kaching.platform.discovery.ServiceId;
import com.kaching.platform.discovery.ServiceKind;
import com.kaching.platform.guice.KachingServices;
import com.kaching.platform.guice.KachingServices.DM;
import com.kaching.platform.guice.Revision;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.queryengine.IsServiceIdBeingRestarted;
import com.kaching.platform.queryengine.IsServiceRevisionRolledBack;
import com.kaching.platform.queryengine.Query;
import com.kaching.platform.queryengine.QueryExecutorService;
import com.kaching.platform.queryengine.StackTraceMonitor;
import com.kaching.platform.queryengine.client.RetryingSmartClient;
import com.kaching.platform.testing.Mockeries.WFMockery;
import com.kaching.platform.testing.WExpectations;
import com.kaching.util.mail.Pager;

public class QueryBasedBaseSelfTestTest {

  private final WFMockery mockery = mockery();
  private QueryExecutorService runner;
  private RetryingSmartClient<DM> dmClient;
  private Pager pager;
  private StackTraceMonitor stm;

  private final Revision revision = new Revision("current-revision");
  private final ServiceKind serviceKind = KachingServices.singleton(KachingServices.BANK.class);
  private final ServiceId serviceId = ServiceId.of("bank3");

  @Before
  public void before() {
    runner = new QueryExecutorServiceStub();
    dmClient = mockery.mockGeneric(new TypeLiteral<>() {});
    pager = mockery.mock(Pager.class);
    stm = mockery.mock(StackTraceMonitor.class);
  }

  @After
  public void after() {
    mockery.assertIsSatisfied();
  }

  @Test
  public void run_noTests_successful() {
    assertFalse(getSelfTest(Collections.emptyList())
        .runSelfTests()
        .hasErrors());
  }

  @Test
  public void runSelfTests_withTestError() {
    assertEquals(new Errors().addMessage("foo"),
        getSelfTest(newArrayList(new ReturnsFooErrorSelfTest())).runSelfTests());
  }

  @Test
  public void runSelfTests_withMultipleTestErrors() {
    assertEquals(
        new Errors().addMessage("foo").addMessage("bar"),
        getSelfTest(
            newArrayList(
                new ReturnsFooErrorSelfTest(),
                new ReturnsBarErrorSelfTest()))
            .runSelfTests());
  }

  @Test
  public void runSelfTests_withTestThatThrowsException() {
    assertEquals(
        new Errors()
            .addMessage("foo")
            .addMessage("bar")
            .addMessage(
                "com.kaching.platform.components.QueryBasedBaseSelfTestTest$ThrowingSelfTest triggered java.lang.RuntimeException: failed"),
        getSelfTest(newArrayList(new ReturnsFooErrorSelfTest(), new ReturnsBarErrorSelfTest(),
            new ThrowingSelfTest())).runSelfTests());
  }

  @Test
  public void runSelfTests_canNotBeRanTwice() {
    QueryBasedBaseSelfTest selfTest = getSelfTest(Collections.emptyList());
    assertFalse(selfTest.runSelfTests().hasErrors());
    assertEquals(new Errors().addMessage("self test can not be ran twice"), selfTest.runSelfTests());
  }

  @Test
  public void run_noErrors_returnsUp() throws Exception {
    mockery.checking(new WExpectations() {{
      oneOf(dmClient).invoke(with(query(new IsServiceIdBeingRestarted(serviceId, new Revision("current-revision")))));
      will(returnValue(false));
    }});
    assertEquals(StartupResult.up(), getSelfTest(ImmutableList.of(new SuccessfulSelfTest())).run());
  }

  @Test
  public void run_shaRevision_noErrors_returnsUp() throws Exception {
    mockery.checking(new WExpectations() {{
      oneOf(dmClient).invoke(with(query(new IsServiceIdBeingRestarted(serviceId, new Revision("9687e577c1")))));
      will(returnValue(false));
    }});
    assertEquals(
        StartupResult.up(),
        getSelfTest(
            new Revision("r9687e577c126398fb402725c00409bdfd1469339"),
            ImmutableList.of(new SuccessfulSelfTest())).run());
  }

  @Test
  public void run_restart_returnsUp() throws Exception {
    mockery.checking(new WExpectations() {{
      oneOf(dmClient).invoke(with(query(new IsServiceIdBeingRestarted(serviceId, new Revision("current-revision")))));
      will(returnValue(true));
    }});
    assertEquals(
        StartupResult.up(),
        getSelfTest(ImmutableList.of(new SuccessfulSelfTest(), new ReturnsFooErrorSelfTest())).run());
  }

  @Test
  public void run_restart_withExceptionTryingToQueryRestartStatus_continueSelfTest() throws Exception {
    mockery.checking(new WExpectations() {{
      oneOf(dmClient).invoke(with(query(new IsServiceIdBeingRestarted(serviceId, new Revision("current-revision")))));
      will(throwException(new RuntimeException("DM is down")));

      oneOf(stm).add(with(exception(RuntimeException.class, "DM is down")));
    }});
    assertEquals(
        StartupResult.up(),
        getSelfTest(ImmutableList.of(new SuccessfulSelfTest())).run());
  }

  @Test
  public void run_withError_notRolledBack_returnsFailed() throws Exception {
    mockery.checking(new WExpectations() {{
      oneOf(dmClient).invoke(with(query(new IsServiceIdBeingRestarted(serviceId, new Revision("current-revision")))));
      will(returnValue(false));

      oneOf(stm).add(with(exception(IllegalStateException.class, "Service bank on current-revision: 1) foo")));

      oneOf(dmClient).invoke(
          with(query(new IsServiceRevisionRolledBack(serviceKind, new Revision("current-revision")))));
      will(returnValue(false));
    }});
    assertEquals(
        StartupResult.failed("Service bank on current-revision: 1) foo"),
        getSelfTest(ImmutableList.of(new SuccessfulSelfTest(), new ReturnsFooErrorSelfTest())).run()
    );
  }

  @Test
  public void run_shaRevision_withError_notRolledBack_returnsFailed() throws Exception {
    mockery.checking(new WExpectations() {{
      oneOf(dmClient).invoke(with(query(new IsServiceIdBeingRestarted(serviceId, new Revision("9687e577c1")))));
      will(returnValue(false));

      oneOf(stm).add(with(exception(IllegalStateException.class, "Service bank on 9687e577c1: 1) foo")));

      oneOf(dmClient).invoke(
          with(query(new IsServiceRevisionRolledBack(serviceKind, new Revision("9687e577c1")))));
      will(returnValue(false));
    }});
    assertEquals(
        StartupResult.failed("Service bank on 9687e577c1: 1) foo"),
        getSelfTest(
            new Revision("r9687e577c126398fb402725c00409bdfd1469339"),
            ImmutableList.of(new SuccessfulSelfTest(), new ReturnsFooErrorSelfTest())).run()
    );
  }

  @Test
  public void run_withError_isRolledBack_pagesAndReturnsUp() throws Exception {
    mockery.checking(new WExpectations() {{
      oneOf(dmClient).invoke(with(query(new IsServiceIdBeingRestarted(serviceId, new Revision("current-revision")))));
      will(returnValue(false));

      oneOf(stm).add(with(exception(IllegalStateException.class, "Service bank on current-revision: 1) foo")));

      oneOf(dmClient).invoke(
          with(query(new IsServiceRevisionRolledBack(serviceKind, new Revision("current-revision")))));
      will(returnValue(true));

      oneOf(pager).alert(with("Overriding Self-Test Failures for bank"),
          with("Service bank failed self-tests. Revision current-revision though is " +
              "rolled-back, so test failures will not stop announcement. Errors: 1) foo"),
          with(aNonNull(Pager.Device.class)));
    }});
    assertEquals(
        StartupResult.up(),
        getSelfTest(ImmutableList.of(new SuccessfulSelfTest(), new ReturnsFooErrorSelfTest())).run()
    );
  }

  @Test
  public void run_shaRevision_withError_isRolledBack_pagesAndReturnsUp() throws Exception {
    mockery.checking(new WExpectations() {{
      oneOf(dmClient).invoke(with(query(new IsServiceIdBeingRestarted(serviceId, new Revision("9687e577c1")))));
      will(returnValue(false));

      oneOf(stm).add(with(exception(IllegalStateException.class, "Service bank on 9687e577c1: 1) foo")));

      oneOf(dmClient).invoke(
          with(query(new IsServiceRevisionRolledBack(serviceKind, new Revision("9687e577c1")))));
      will(returnValue(true));

      oneOf(pager).alert(with("Overriding Self-Test Failures for bank"),
          with("Service bank failed self-tests. Revision 9687e577c1 though is " +
              "rolled-back, so test failures will not stop announcement. Errors: 1) foo"),
          with(aNonNull(Pager.Device.class)));
    }});
    assertEquals(
        StartupResult.up(),
        getSelfTest(
            new Revision("r9687e577c126398fb402725c00409bdfd1469339"),
            ImmutableList.of(new SuccessfulSelfTest(), new ReturnsFooErrorSelfTest())).run()
    );
  }

  @Test
  public void run_withError_withExceptionTryingToQueryRollbackStatus_returnsFailed() throws Exception {
    mockery.checking(new WExpectations() {{
      oneOf(dmClient).invoke(with(query(new IsServiceIdBeingRestarted(serviceId, new Revision("current-revision")))));
      will(returnValue(false));

      oneOf(stm).add(with(exception(IllegalStateException.class, "Service bank on current-revision: 1) foo")));

      oneOf(dmClient).invoke(
          with(query(new IsServiceRevisionRolledBack(serviceKind, new Revision("current-revision")))));
      will(throwException(new RuntimeException("DM is down")));

      oneOf(stm).add(with(exception(RuntimeException.class, "DM is down")));
    }});
    assertEquals(
        StartupResult.failed("Service bank on current-revision: 1) foo"),
        getSelfTest(ImmutableList.of(new SuccessfulSelfTest(), new ReturnsFooErrorSelfTest())).run()
    );
  }

  private static class SuccessfulSelfTest extends AbstractQuery<Errors> {

    @Override
    public Errors process() {
      return new Errors();
    }

  }

  private static class ReturnsFooErrorSelfTest extends AbstractQuery<Errors> {

    @Override
    public Errors process() {
      return new Errors().addMessage("foo");
    }

  }

  private static class ReturnsBarErrorSelfTest extends AbstractQuery<Errors> {

    @Override
    public Errors process() {
      return new Errors().addMessage("bar");
    }

  }

  private static class ThrowingSelfTest extends AbstractQuery<Errors> {

    @Override
    public Errors process() {
      throw new RuntimeException("failed");
    }

  }

  private QueryBasedBaseSelfTest getSelfTest(Collection<? extends Query<Errors>> selfTests) {
    return new QueryBasedBaseSelfTest(runner, selfTests, serviceId, serviceKind, dmClient, revision, pager, stm);
  }

  private QueryBasedBaseSelfTest getSelfTest(Revision revision, Collection<? extends Query<Errors>> selfTests) {
    return new QueryBasedBaseSelfTest(runner, selfTests, serviceId, serviceKind, dmClient, revision, pager, stm);
  }

}
