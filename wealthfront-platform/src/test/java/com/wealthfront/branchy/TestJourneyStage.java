package com.wealthfront.branchy;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@ExposeType(value = ExposeTo.BACKEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
enum TestJourneyStage {
  RESEARCHING {
    @Override
    public <T> T visit(JourneyStageVisitor<T> visitor) {
      return visitor.visitResearching();
    }
  },
  OPEN_HOUSES {
    @Override
    public <T> T visit(JourneyStageVisitor<T> visitor) {
      return visitor.visitOpenHouses();
    }
  },
  MAKING_OFFERS {
    @Override
    public <T> T visit(JourneyStageVisitor<T> visitor) {
      return visitor.visitMakingOffers();
    }
  },
  IN_CONTRACT {
    @Override
    public <T> T visit(JourneyStageVisitor<T> visitor) {
      return visitor.visitInContract();
    }
  };

  public abstract <T> T visit(JourneyStageVisitor<T> visitor);

  public interface JourneyStageVisitor<T> {
    T visitResearching();

    T visitOpenHouses();

    T visitMakingOffers();

    T visitInContract();
  }
}
