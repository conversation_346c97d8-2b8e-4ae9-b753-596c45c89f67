package com.wealthfront.branchy;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@ExposeType(value = ExposeTo.BACKEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
enum TestCreditPullRequestType {
  INDIVIDUAL {
    @Override
    public <T> T visit(CreditPullRequestTypeVisitor<T> visitor) {
      return visitor.visitIndividual();
    }
  },
  JOINT {
    @Override
    public <T> T visit(CreditPullRequestTypeVisitor<T> visitor) {
      return visitor.visitJoint();
    }
  };

  public abstract <T> T visit(CreditPullRequestTypeVisitor<T> visitor);

  public interface CreditPullRequestTypeVisitor<T> {
    T visitIndividual();

    T visitJoint();
  }
}
