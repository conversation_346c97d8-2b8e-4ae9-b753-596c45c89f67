package com.wealthfront.branchy;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@ExposeType(value = ExposeTo.BACKEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
enum TestCreditPullType {
  HARD {
    @Override
    public <T> T visit(CreditPullTypeVisitor<T> visitor) {
      return visitor.visitHard();
    }
  },
  SOFT {
    @Override
    public <T> T visit(CreditPullTypeVisitor<T> visitor) {
      return visitor.visitSoft();
    }
  };
  public abstract <T> T visit(CreditPullTypeVisitor<T> visitor);

  public interface CreditPullTypeVisitor<T> {
    T visitHard();

    T visitSoft();
  }
}