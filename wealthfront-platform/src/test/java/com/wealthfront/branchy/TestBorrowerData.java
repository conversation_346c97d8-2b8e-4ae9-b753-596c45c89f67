package com.wealthfront.branchy;

import java.util.Collections;
import java.util.List;
import java.util.Set;

import org.joda.time.DateTime;
import org.joda.time.LocalDate;

import com.kaching.entities.EmailAddress;
import com.kaching.entities.PhoneNumber;
import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@Entity(discriminator = "borrower-data")
public class TestBorrowerData extends TestMortgageData {

  private Set<String> fieldsToNullSet;

  @Value(optional = true)
  private TestEntityAction action;

  @Value(optional = true)
  private String id;

  @Value(optional = true)
  private TestIncomeWorkflowData incomeWorkflowData;

  @Value(optional = true)
  private TestBorrowerType borrowerType;

  @Value(optional = true)
  private String firstName;

  @Value(optional = true)
  private String middleName;

  @Value(optional = true)
  private String lastName;

  @Value(optional = true)
  private String suffix;

  @Value(optional = true)
  private List<String> alternativeNames;

  @Value(optional = true)
  private DateTime termsOfServiceConsentedAt;

  @Value(optional = true)
  private DateTime creditPullConsentedAt;

  @Value(optional = true)
  private EmailAddress emailAddress;

  @Value(optional = true)
  private PhoneNumber phoneNumber;

  @Value(optional = true)
  private LocalDate dateOfBirth;

  @Value(optional = true)
  private TestAddressData currentAddress;

  @Value(optional = true)
  private TestAddressData mailingAddress;

  @Value(optional = true)
  private TestMaritalStatus maritalStatus;

  @Value(optional = true)
  private TestCitizenshipType citizenshipType;

  @Value(optional = true)
  private String spouseBorrowerId;

  @Value(optional = true)
  private Boolean isCurrentlyLivingWithCoBorrowers;

  @Value(optional = true)
  private Boolean isIntendingToOccupy;

  @Value(optional = true)
  private Boolean isPrimaryAuthorized;

  @Value(optional = true)
  private Boolean isSharingInformationWithCoBorrowers;

  @Value(optional = true)
  private TestMilitaryStatus militaryStatus;

  @Value(optional = true)
  private LocalDate militaryServiceExpectedCompletionDate;

  @Value(optional = true)
  private TestEmploymentStatus employmentStatus;

  @Value(optional = true)
  private TestCreditPullConsentType softCreditPullConsentType;

  @Value(optional = true)
  private TestCreditPullStatus creditPullStatus;

  @Value(optional = true)
  private TestCreditPullType creditPullType;

  @Value(optional = true)
  private List<TestCreditScoreData> creditScoreData;

  @Value(optional = true)
  private TestBorrowerWorkflowData borrowerWorkflowData;

  @Value(optional = true)
  private Boolean homeownerPastThreeYears;

  @Value(optional = true)
  private Boolean outstandingJudgmentsIndicator;

  @Value(optional = true)
  private Boolean presentlyDelinquentIndicator;

  @Value(optional = true)
  private Boolean partyToLawsuitIndicator;

  @Value(optional = true)
  private Boolean priorPropertyDeedInLieuConveyedIndicator;

  @Value(optional = true)
  private Boolean priorPropertyShortSaleCompletedIndicator;

  @Value(optional = true)
  private Boolean priorPropertyForeclosureCompletedIndicator;

  @Value(optional = true)
  private Boolean bankruptcyIndicator;

  @Value(optional = true)
  private List<TestBankruptcyData> bankruptcies;

  TestBorrowerData() { /* JSON */ }

  TestBorrowerData(
          TestEntityAction action, TestIncomeWorkflowData incomeWorkflowData, TestBorrowerType borrowerType, String firstName, String middleName,
          String lastName, String suffix, List<String> alternativeNames, DateTime termsOfServiceConsentedAt, DateTime creditPullConsentedAt,
          EmailAddress emailAddress, PhoneNumber phoneNumber, LocalDate dateOfBirth, TestAddressData currentAddress,
          TestAddressData mailingAddress, TestMaritalStatus maritalStatus, TestCitizenshipType citizenshipType, String spouseBorrowerId,
          Boolean isCurrentlyLivingWithCoBorrowers, Boolean isIntendingToOccupy, Boolean isPrimaryAuthorized,
          Boolean isSharingInformationWithCoBorrowers, TestMilitaryStatus militaryStatus,
          LocalDate militaryServiceExpectedCompletionDate, TestEmploymentStatus employmentStatus, TestBorrowerWorkflowData borrowerWorkflowData,
          Boolean homeownerPastThreeYears, Boolean outstandingJudgmentsIndicator, Boolean presentlyDelinquentIndicator,
          Boolean partyToLawsuitIndicator, Boolean priorPropertyDeedInLieuConveyedIndicator, Boolean priorPropertyShortSaleCompletedIndicator,
          Boolean priorPropertyForeclosureCompletedIndicator, Boolean bankruptcyIndicator, List<TestBankruptcyData> bankruptcies, TestCreditPullConsentType softTestCreditPullConsentType, TestCreditPullStatus creditPullStatus,
      TestCreditPullType creditPullType, List<TestCreditScoreData> creditScoreData) {
    this.action = action;
    this.incomeWorkflowData = incomeWorkflowData;
    this.borrowerType = borrowerType;
    this.firstName = firstName;
    this.middleName = middleName;
    this.lastName = lastName;
    this.suffix = suffix;
    this.alternativeNames = alternativeNames;
    this.termsOfServiceConsentedAt = termsOfServiceConsentedAt;
    this.creditPullConsentedAt = creditPullConsentedAt;
    this.emailAddress = emailAddress;
    this.phoneNumber = phoneNumber;
    this.dateOfBirth = dateOfBirth;
    this.currentAddress = currentAddress;
    this.mailingAddress = mailingAddress;
    this.maritalStatus = maritalStatus;
    this.citizenshipType = citizenshipType;
    this.spouseBorrowerId = spouseBorrowerId;
    this.isCurrentlyLivingWithCoBorrowers = isCurrentlyLivingWithCoBorrowers;
    this.isIntendingToOccupy = isIntendingToOccupy;
    this.isPrimaryAuthorized = isPrimaryAuthorized;
    this.isSharingInformationWithCoBorrowers = isSharingInformationWithCoBorrowers;
    this.militaryStatus = militaryStatus;
    this.militaryServiceExpectedCompletionDate = militaryServiceExpectedCompletionDate;
    this.employmentStatus = employmentStatus;
    this.softCreditPullConsentType = softCreditPullConsentType;
    this.creditPullStatus = creditPullStatus;
    this.creditPullType = creditPullType;
    this.creditScoreData = creditScoreData;
    this.borrowerWorkflowData = borrowerWorkflowData;
    this.homeownerPastThreeYears = homeownerPastThreeYears;
    this.outstandingJudgmentsIndicator = outstandingJudgmentsIndicator;
    this.presentlyDelinquentIndicator = presentlyDelinquentIndicator;
    this.partyToLawsuitIndicator = partyToLawsuitIndicator;
    this.priorPropertyDeedInLieuConveyedIndicator = priorPropertyDeedInLieuConveyedIndicator;
    this.priorPropertyShortSaleCompletedIndicator = priorPropertyShortSaleCompletedIndicator;
    this.priorPropertyForeclosureCompletedIndicator = priorPropertyForeclosureCompletedIndicator;
    this.bankruptcyIndicator = bankruptcyIndicator;
    this.bankruptcies = bankruptcies;
  }

  public String getId() {
    return id;
  }

  public Option<TestIncomeWorkflowData> getIncomeWorkflowData() {
    return Option.of(incomeWorkflowData);
  }

  public Option<TestBorrowerType> getBorrowerType() {
    return Option.of(borrowerType);
  }

  public Option<String> getFirstName() {
    return Option.of(firstName);
  }

  public Option<String> getMiddleName() {
    return Option.of(middleName);
  }

  public Option<String> getLastName() {
    return Option.of(lastName);
  }

  public Option<String> getSuffix() {
    return Option.of(suffix);
  }

  public Option<List<String>> getAlternativeNames() {
    return Option.of(alternativeNames);
  }

  public Option<DateTime> getTermsOfServiceConsentedAt() {
    return Option.of(termsOfServiceConsentedAt);
  }

  public Option<DateTime> getCreditPullConsentedAt() {
    return Option.of(creditPullConsentedAt);
  }

  public Option<EmailAddress> getEmailAddress() {
    return Option.of(emailAddress);
  }

  public Option<PhoneNumber> getPhoneNumber() {
    return Option.of(phoneNumber);
  }

  public Option<LocalDate> getDateOfBirth() {
    return Option.of(dateOfBirth);
  }

  public Option<TestAddressData> getCurrentAddress() {
    return Option.of(currentAddress);
  }

  public Option<TestAddressData> getMailingAddress() {
    return Option.of(mailingAddress);
  }

  public Option<TestMaritalStatus> getMaritalStatus() {
    return Option.of(maritalStatus);
  }

  public Option<TestCitizenshipType> getCitizenshipType() {
    return Option.of(citizenshipType);
  }

  public Option<String> getSpouseBorrowerId() {
    return Option.of(spouseBorrowerId);
  }

  public Option<Boolean> getCurrentlyLivingWithCoBorrowers() {
    return Option.of(isCurrentlyLivingWithCoBorrowers);
  }

  public Option<Boolean> getIntendingToOccupy() {
    return Option.of(isIntendingToOccupy);
  }

  public Option<Boolean> getPrimaryAuthorized() {
    return Option.of(isPrimaryAuthorized);
  }

  public Option<Boolean> getSharingInformationWithCoBorrowers() {
    return Option.of(isSharingInformationWithCoBorrowers);
  }

  public Option<TestMilitaryStatus> getMilitaryStatus() {
    return Option.of(militaryStatus);
  }

  public Option<LocalDate> getMilitaryServiceExpectedCompletionDate() {
    return Option.of(militaryServiceExpectedCompletionDate);
  }

  public Option<TestEmploymentStatus> getEmploymentStatus() {
    return Option.of(employmentStatus);
  }

  public Option<TestCreditPullConsentType> getSoftCreditPullConsentType() {
    return Option.of(softCreditPullConsentType);
  }

  public Option<TestCreditPullStatus> getCreditPullStatus() {
    return Option.of(creditPullStatus);
  }

  public Option<TestCreditPullType> getCreditPullType() {
    return Option.of(creditPullType);
  }

  public Option<List<TestCreditScoreData>> getCreditScoreData() {
    return Option.of(creditScoreData);
  }

  public Option<TestBorrowerWorkflowData> getBorrowerWorkflowData() {
    return Option.of(borrowerWorkflowData);
  }

  public Option<Boolean> getHomeownerPastThreeYears() {
    return Option.of(homeownerPastThreeYears);
  }

  public Option<Boolean> getOutstandingJudgmentsIndicator() {
    return Option.of(outstandingJudgmentsIndicator);
  }

  public Option<Boolean> getPresentlyDelinquentIndicator() {
    return Option.of(presentlyDelinquentIndicator);
  }

  public Option<Boolean> getPartyToLawsuitIndicator() {
    return Option.of(partyToLawsuitIndicator);
  }

  public Option<Boolean> getPriorPropertyDeedInLieuConveyedIndicator() {
    return Option.of(priorPropertyDeedInLieuConveyedIndicator);
  }

  public Option<Boolean> getPriorPropertyShortSaleCompletedIndicator() {
    return Option.of(priorPropertyShortSaleCompletedIndicator);
  }

  public Option<Boolean> getPriorPropertyForeclosureCompletedIndicator() {
    return Option.of(priorPropertyForeclosureCompletedIndicator);
  }

  public Option<Boolean> getBankruptcyIndicator() {
    return Option.of(bankruptcyIndicator);
  }

  public Option<List<TestBankruptcyData>> getBankruptcies() {
    return Option.of(bankruptcies);
  }

  public void setId(String id) {
    this.id = id;
  }

  @Override
  public TestEntityAction getAction() {
    return action;
  }

  @Override
  public void setAction(TestEntityAction action) {
    this.action = action;
  }

  @Override
  public TestDataType getDataType() {
    return TestDataType.BORROWER;
  }

  @Override
  public String getInternalId() {
    return id;
  }

  @Override
  public void setInternalId(String id) {
    this.id = id;
  }

  @Override
  public void setFieldsToNullSet(Set<String> fieldsToNullSet) {
    this.fieldsToNullSet = fieldsToNullSet;
  }

  @Override
  public Set<String> getFieldsToNullSet() {
    return fieldsToNullSet == null ? Collections.emptySet() : fieldsToNullSet;
  }

  @Override
  public TestVestaOperationType getVestaOperationType() {
    return TestVestaOperationType.APPLICATION_UPDATE;
  }

}
