package com.wealthfront.branchy;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@ExposeType(value = ExposeTo.BACKEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
enum TestCreditPullConsentType {
  VERBAL {
    @Override
    public <T> T visit(CreditPullConsentTypeVisitor<T> visitor) {
      return visitor.visitVerbal();
    }
  },
  WRITTEN {
    @Override
    public <T> T visit(CreditPullConsentTypeVisitor<T> visitor) {
      return visitor.visitWritten();
    }
  },
  ELECTRONIC {
    @Override
    public <T> T visit(CreditPullConsentTypeVisitor<T> visitor) {
      return visitor.visitElectronic();
    }
  };

  public abstract <T> T visit(CreditPullConsentTypeVisitor<T> visitor);

  public interface CreditPullConsentTypeVisitor<T> {
    T visitVerbal();

    T visitWritten();

    T visitElectronic();
  }
}
