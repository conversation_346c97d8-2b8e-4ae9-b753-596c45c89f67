package com.wealthfront.branchy;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@ExposeType(value = ExposeTo.BACKEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
enum TestMilitaryStatus {
  NONE {
    @Override
    public <T> T visit(MilitaryStatusVisitor<T> visitor) {
      return visitor.visitNone();
    }
  },
  ACTIVE_DUTY {
    @Override
    public <T> T visit(MilitaryStatusVisitor<T> visitor) {
      return visitor.visitActiveDuty();
    }
  },
  RESERVE_NEVER_ACTIVATED {
    @Override
    public <T> T visit(MilitaryStatusVisitor<T> visitor) {
      return visitor.visitReserveNeverActivated();
    }
  },
  SURVIVING_SPOUSE {
    @Override
    public <T> T visit(MilitaryStatusVisitor<T> visitor) {
      return visitor.visitSurvivingSpouse();
    }
  },
  VETERAN {
    @Override
    public <T> T visit(MilitaryStatusVisitor<T> visitor) {
      return visitor.visitVeteran();
    }
  };

  public boolean isActive() {
    return this.visit(new MilitaryStatusVisitor<>() {
      @Override
      public Boolean visitNone() {
        return false;
      }

      @Override
      public Boolean visitActiveDuty() {
        return true;
      }

      @Override
      public Boolean visitReserveNeverActivated() {
        return true;
      }

      @Override
      public Boolean visitSurvivingSpouse() {
        return false;
      }

      @Override
      public Boolean visitVeteran() {
        return false;
      }
    });
  }

  public abstract <T> T visit(MilitaryStatusVisitor<T> visitor);

  public interface MilitaryStatusVisitor<T> {
    T visitNone();

    T visitActiveDuty();

    T visitReserveNeverActivated();

    T visitSurvivingSpouse();

    T visitVeteran();
  }
}
