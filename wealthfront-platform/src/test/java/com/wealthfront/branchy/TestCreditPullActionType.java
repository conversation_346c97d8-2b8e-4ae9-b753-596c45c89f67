package com.wealthfront.branchy;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@ExposeType(value = ExposeTo.BACKEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
enum TestCreditPullActionType {
  PULL_NEW {
    @Override
    public <T> T visit(CreditPullActionTypeVisitor<T> visitor) {
      return visitor.visitPullNew();
    }
  },
  UPGRADE {
    @Override
    public <T> T visit(CreditPullActionTypeVisitor<T> visitor) {
      return visitor.visitUpgrade();
    }
  },
  RETRIEVE {
    @Override
    public <T> T visit(CreditPullActionTypeVisitor<T> visitor) {
      return visitor.visitRetrieve();
    }
  };

  public abstract <T> T visit(CreditPullActionTypeVisitor<T> visitor);

  public interface CreditPullActionTypeVisitor<T> {
    T visitPullNew();

    T visitUpgrade();

    T visitRetrieve();
  }
}