package com.wealthfront.branchy;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@ExposeType(value = ExposeTo.BACKEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
enum TestMaritalStatus {
  MARRIED {
    @Override
    public <T> T visit(MaritalStatusVisitor<T> visitor) {
      return visitor.visitMarried();
    }
  },
  SEPARATED {
    @Override
    public <T> T visit(MaritalStatusVisitor<T> visitor) {
      return visitor.visitSeparated();
    }
  },
  UNMARRIED {
    @Override
    public <T> T visit(MaritalStatusVisitor<T> visitor) {
      return visitor.visitUnmarried();
    }
  };

  public abstract <T> T visit(MaritalStatusVisitor<T> visitor);

  public interface MaritalStatusVisitor<T> {
    T visitMarried();

    T visitSeparated();

    T visitUnmarried();
  }
}
