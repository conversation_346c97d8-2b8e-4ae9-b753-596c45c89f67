package com.wealthfront.branchy;

import java.util.Collections;
import java.util.Set;

import org.joda.time.DateTime;

import com.kaching.platform.common.Option;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@Entity(discriminator = "borrower-workflow-data")
class TestBorrowerWorkflowData extends TestMortgageData {
  private Set<String> fieldsToNullSet;

  @Value(optional = true)
  private TestEntityAction action;

  @Value
  private String id;

  @Value(optional = true)
  private DateTime createdAt;

  @Value(optional = true)
  private String activeCreditPullId;

  TestBorrowerWorkflowData() { /* JSON */ }

  TestBorrowerWorkflowData(
      TestEntityAction action, String id, DateTime createdAt, String activeCreditPullId) {
    this.action = action;
    this.id = id;
    this.createdAt = createdAt;
    this.activeCreditPullId = activeCreditPullId;
  }

  public Option<DateTime> getCreatedAt() {
    return Option.of(createdAt);
  }

  public Option<String> getActiveCreditPullId() {
    return Option.of(activeCreditPullId);
  }

  @Override
  public String getInternalId() {
    return id;
  }

  @Override
  public void setInternalId(String id) {
    this.id = id;
  }

  @Override
  public void setFieldsToNullSet(Set<String> fieldsToNullSet) {
    this.fieldsToNullSet = fieldsToNullSet;
  }

  @Override
  public Set<String> getFieldsToNullSet() {
    return fieldsToNullSet == null ? Collections.emptySet() : fieldsToNullSet;
  }

  @Override
  public TestEntityAction getAction() {
    return action;
  }

  @Override
  public void setAction(TestEntityAction action) {
    this.action = action;
  }

  @Override
  public TestDataType getDataType() {
    return TestDataType.DOCUMENT_WORKFLOW;
  }

  @Override
  public TestVestaOperationType getVestaOperationType() {
    return TestVestaOperationType.NO_OP;
  }
}
