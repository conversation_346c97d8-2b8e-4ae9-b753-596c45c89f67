package com.wealthfront.branchy;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@ExposeType(value = ExposeTo.BACKEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
enum TestCreditPullStatus {
  NOT_STARTED {
    @Override
    public <T> T visit(CreditPullStatusVisitor<T> visitor) {
      return visitor.visitNotStarted();
    }
  },
  IN_PROGRESS {
    @Override
    public <T> T visit(CreditPullStatusVisitor<T> visitor) {
      return visitor.visitInProgress();
    }
  },
  FAILED {
    @Override
    public <T> T visit(CreditPullStatusVisitor<T> visitor) {
      return visitor.visitFailed();
    }
  },
  SUCCEEDED {
    @Override
    public <T> T visit(CreditPullStatusVisitor<T> visitor) {
      return visitor.visitSucceeded();
    }
  };

  public abstract <T> T visit(CreditPullStatusVisitor<T> visitor);

  public interface CreditPullStatusVisitor<T> {
    T visitNotStarted();

    T visitInProgress();

    T visitFailed();

    T visitSucceeded();
  }
}