package com.wealthfront.branchy;

import static com.wealthfront.branchy.TestEntityAction.UPDATE;

import java.util.Collections;
import java.util.List;
import java.util.Set;

import com.kaching.platform.common.Option;
import com.kaching.platform.common.Strings;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@Entity(discriminator = "application-data")
public class TestApplicationData extends TestMortgageData {

  private Set<String> fieldsToNullSet;

  @Value(optional = true)
  private String id;

  @Value(optional = true)
  private TestMortgageType mortgageType;

  @Value(optional = true)
  private List<TestBorrowerData> borrowers;

  @Value(optional = true)
  private TestLoanAmountData loanAmount;

  @Value(optional = true)
  private List<TestCreditPullData> creditPulls;

  TestApplicationData() { /* JSON */ }

  public Option<TestMortgageType> getMortgageType() {
    return Option.of(mortgageType);
  }

  public Option<List<TestBorrowerData>> getBorrowers() {
    return Option.of(borrowers);
  }

  public Option<TestLoanAmountData> getLoanAmount() {
    return Option.of(loanAmount);
  }

  public Option<List<TestCreditPullData>> getCreditPulls() {
    return Option.of(creditPulls);
  }

  @Override
  public TestVestaOperationType getVestaOperationType() {
    return TestVestaOperationType.APPLICATION_UPDATE;
  }

  @Override
  public String getInternalId() {
    return id.toString();
  }

  @Override
  public void setInternalId(String id) {
    this.id = id;
  }

  @Override
  public void setFieldsToNullSet(Set<String> fieldsToNullSet) {
    this.fieldsToNullSet = fieldsToNullSet;
  }

  @Override
  public Set<String> getFieldsToNullSet() {
    return fieldsToNullSet == null ? Collections.emptySet() : fieldsToNullSet;
  }

  @Override
  public TestEntityAction getAction() {
    return TestEntityAction.UPDATE;
  }

  @Override
  public void setAction(TestEntityAction action) {
    throw new RuntimeException(Strings.format("TestApplicationData must always have Entity Action %s", UPDATE));
  }

  @Override
  public TestDataType getDataType() {
    return TestDataType.LOAN;
  }

}
