package com.wealthfront.branchy;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@ExposeType(value = ExposeTo.BACKEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
enum TestCreditBureau {
  EQUIFAX {
    @Override
    public <T> T visit(CreditBureauVisitor<T> visitor) {
      return visitor.visitEquifax();
    }
  },
  EXPERIAN {
    @Override
    public <T> T visit(CreditBureauVisitor<T> visitor) {
      return visitor.visitExperian();
    }
  },
  TRANSUNION {
    @Override
    public <T> T visit(CreditBureauVisitor<T> visitor) {
      return visitor.visitTransunion();
    }
  },
  OTHER {
    @Override
    public <T> T visit(CreditBureauVisitor<T> visitor) {
      return visitor.visitOther();
    }
  };

  public abstract <T> T visit(CreditBureauVisitor<T> visitor);

  public interface CreditBureauVisitor<T> {
    T visitEquifax();

    T visitExperian();

    T visitTransunion();

    T visitOther();
  }
}
