package com.wealthfront.branchy;

import java.util.Set;

import com.kaching.annotations.BranchyDataEntity;
import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@ExposeType(value = ExposeTo.BACKEND, namespace = ExposeType.RewriteNamespace.SERVICE)
@Entity(discriminatorName = "type", subclasses = {
    TestAddressData.class,
    TestApplicationData.class,
    TestBankruptcyData.class,
    TestBorrowerData.class,
    TestBorrowerWorkflowData.class,
    TestCreditPullData.class,
    TestCreditScoreData.class,
    TestIncomeWorkflowData.class,
    TestLoanAmountData.class,
})
public abstract class TestMortgageData implements BranchyDataEntity {

  @Value(
      optional = true,
      nullable = true
  )
  public abstract String getId();

  public abstract void setId(String id);

  @Value(
      optional = true,
      nullable = true
  )
  public abstract Set<String> getFieldsToNullSet();

  public abstract void setFieldsToNullSet(Set<String> fieldsToNullSet);

  @Value(
      optional = true,
      nullable = true
  )
  public abstract TestEntityAction getAction();

  public abstract void setAction(TestEntityAction action);

  @Value(
      optional = true,
      nullable = true
  )
  public abstract TestDataType getDataType();

  @Value(
      optional = true,
      nullable = true
  )
  public abstract TestVestaOperationType getVestaOperationType();

}
