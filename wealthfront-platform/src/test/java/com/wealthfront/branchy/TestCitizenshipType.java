package com.wealthfront.branchy;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@ExposeType(value = ExposeTo.BACKEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
enum TestCitizenshipType {
  US_CITIZEN {
    @Override
    public <T> T visit(CitizenshipTypeVisitor<T> visitor) {
      return visitor.visitUsCitizen();
    }
  },
  PERMANENT_RESIDENT {
    @Override
    public <T> T visit(CitizenshipTypeVisitor<T> visitor) {
      return visitor.visitPermanentResident();
    }
  },
  NON_PERMANENT_RESIDENT {
    @Override
    public <T> T visit(CitizenshipTypeVisitor<T> visitor) {
      return visitor.visitNonPermanentResident();
    }
  };

  public abstract <T> T visit(CitizenshipTypeVisitor<T> visitor);

  public interface CitizenshipTypeVisitor<T> {
    T visitUsCitizen();

    T visitPermanentResident();

    T visitNonPermanentResident();
  }
}
