package com.wealthfront.branchy;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@ExposeType(value = ExposeTo.BACKEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
enum TestGiftSource {
  COMMUNITY_NON_PROFIT("Community non-profit") {
    @Override
    public <T> T visit(GiftSourceVisitor<T> visitor) {
      return visitor.caseCommunityNonProfit();
    }
  },
  EMPLOYER("Employer") {
    @Override
    public <T> T visit(GiftSourceVisitor<T> visitor) {
      return visitor.caseEmployer();
    }
  },
  FEDERAL_AGENCY("Federal agency") {
    @Override
    public <T> T visit(GiftSourceVisitor<T> visitor) {
      return visitor.caseFederalAgency();
    }
  },
  LENDER("Lender") {
    @Override
    public <T> T visit(GiftSourceVisitor<T> visitor) {
      return visitor.caseLender();
    }
  },
  LOCAL_AGENCY("Local agency") {
    @Override
    public <T> T visit(GiftSourceVisitor<T> visitor) {
      return visitor.caseLocalAgency();
    }
  },
  NON_PARENT_RELATIVE("A relative that is not your parent") {
    @Override
    public <T> T visit(GiftSourceVisitor<T> visitor) {
      return visitor.caseNonParentRelative();
    }
  },
  OTHER("Other") {
    @Override
    public <T> T visit(GiftSourceVisitor<T> visitor) {
      return visitor.caseOther();
    }
  },
  PARENT("Parent") {
    @Override
    public <T> T visit(GiftSourceVisitor<T> visitor) {
      return visitor.caseParent();
    }
  },
  RELATIVE("Relative") {
    @Override
    public <T> T visit(GiftSourceVisitor<T> visitor) {
      return visitor.caseRelative();
    }
  },
  RELIGIOUS_NON_PROFIT("Religious non-profit") {
    @Override
    public <T> T visit(GiftSourceVisitor<T> visitor) {
      return visitor.caseReligiousNonProfit();
    }
  },
  STATE_AGENCY("State agency") {
    @Override
    public <T> T visit(GiftSourceVisitor<T> visitor) {
      return visitor.caseStateAgency();
    }
  },
  UNMARRIED_PARTNER("Unmarried partner") {
    @Override
    public <T> T visit(GiftSourceVisitor<T> visitor) {
      return visitor.caseUnmarriedPartner();
    }
  },
  UNRELATED_FRIEND("Friend") {
    @Override
    public <T> T visit(GiftSourceVisitor<T> visitor) {
      return visitor.caseUnrelatedFriend();
    }
  };

  private final String description;

  TestGiftSource(String description) {
    this.description = description;
  }

  public String getDescription() {
    return description;
  }

  public abstract <T> T visit(GiftSourceVisitor<T> visitor);

  public interface GiftSourceVisitor<T> {

    T caseCommunityNonProfit();

    T caseEmployer();

    T caseFederalAgency();

    T caseLender();

    T caseLocalAgency();

    T caseNonParentRelative();

    T caseOther();

    T caseParent();

    T caseRelative();

    T caseReligiousNonProfit();

    T caseStateAgency();

    T caseUnmarriedPartner();

    T caseUnrelatedFriend();
  }
}
