package com.wealthfront.branchy;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;

@ExposeType(value = ExposeTo.BACKEND, namespace = ExposeType.RewriteNamespace.DO_NOT_COPY)
public enum TestDataType {
  LOAN("Loan") {
    @Override
    public <T> T visit(DataTypeVisitor<T> visitor) {
      return visitor.caseLoan();
    }
  },
  INCOME("Income") {
    @Override
    public <T> T visit(DataTypeVisitor<T> visitor) {
      return visitor.caseIncome();
    }
  },
  ASSET("Asset") {
    @Override
    public <T> T visit(DataTypeVisitor<T> visitor) {
      return visitor.caseAsset();
    }
  },
  BORROWER("Borrower") {
    @Override
    public <T> T visit(DataTypeVisitor<T> visitor) {
      return visitor.caseBorrower();
    }
  },
  ADDRESS("Address") {
    @Override
    public <T> T visit(DataTypeVisitor<T> visitor) {
      return visitor.caseAddress();
    }
  },
  CONTACT("Contact") {
    @Override
    public <T> T visit(DataTypeVisitor<T> visitor) {
      return visitor.caseContact();
    }
  },
  CREDIT_PULL("Credit Pull") {
    @Override
    public <T> T visit(DataTypeVisitor<T> visitor) {
      return visitor.caseCreditPull();
    }
  },
  CREDIT_SCORE("Credit Score") {
    @Override
    public <T> T visit(DataTypeVisitor<T> visitor) {
      return visitor.caseCreditScore();
    }
  },
  DOCUMENT("Document") {
    @Override
    public <T> T visit(DataTypeVisitor<T> visitor) {
      return visitor.caseDocument();
    }
  },
  FILE(null) {
    @Override
    public <T> T visit(DataTypeVisitor<T> visitor) {
      return visitor.caseFile();
    }
  },
  INCOME_WORKFLOW(null) {
    @Override
    public <T> T visit(DataTypeVisitor<T> visitor) {
      return visitor.caseIncomeWorkflow();
    }
  },
  EMPLOYMENT_WORKFLOW(null) {
    @Override
    public <T> T visit(DataTypeVisitor<T> visitor) {
      return visitor.caseEmploymentWorkflow();
    }
  },
  DOCUMENT_WORKFLOW(null) {
    @Override
    public <T> T visit(DataTypeVisitor<T> visitor) {
      return visitor.caseDocumentWorkflow();
    }
  },
  ASSET_WORKFLOW(null) {
    @Override
    public <T> T visit(DataTypeVisitor<T> visitor) {
      return visitor.caseAssetWorkflow();
    }
  },
  PROPERTY("Property") {
    @Override
    public <T> T visit(DataTypeVisitor<T> visitor) {
      return visitor.caseProperty();
    }
  },
  LOAN_AMOUNT("LoanAmount") {
    @Override
    public <T> T visit(DataTypeVisitor<T> visitor) {
      return visitor.caseLoanAmount();
    }
  },
  OWNED_PROPERTY("Owned Property") {
    @Override
    public <T> T visit(DataTypeVisitor<T> visitor) {
      return visitor.caseOwnedProperty();
    }
  },
  OWNED_PROPERTY_LIABILITY("Owned Property Liability") {
    @Override
    public <T> T visit(DataTypeVisitor<T> visitor) {
      return visitor.caseOwnedPropertyLiability();
    }
  },
  BORROWER_WORKFLOW(null) {
    @Override
    public <T> T visit(DataTypeVisitor<T> visitor) {
      return visitor.caseBorrowerWorkflow();
    }
  },
  BANKRUPTCY("Bankruptcy") {
    @Override
    public <T> T visit(DataTypeVisitor<T> visitor) {
      return visitor.caseBankruptcy();
    }
  };

  private final String value;

  TestDataType(String value) {
    this.value = value;
  }

  public String getValue() {
    return value;
  }

  public abstract <T> T visit(DataTypeVisitor<T> visitor);

  public interface DataTypeVisitor<T> {

    T caseLoan();

    T caseIncome();

    T caseAsset();

    T caseBorrower();

    T caseAddress();

    T caseContact();

    T caseDocument();

    T caseFile();

    T caseAssetWorkflow();

    T caseIncomeWorkflow();

    T caseEmploymentWorkflow();

    T caseDocumentWorkflow();

    T caseProperty();

    T caseLoanAmount();

    T caseOwnedProperty();

    T caseOwnedPropertyLiability();

    T caseBorrowerWorkflow();

    T caseBankruptcy();

    T caseCreditScore();

    T caseCreditPull();

  }
}
