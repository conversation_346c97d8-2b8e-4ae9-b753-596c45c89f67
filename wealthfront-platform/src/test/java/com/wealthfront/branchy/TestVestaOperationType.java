package com.wealthfront.branchy;

public enum TestVestaOperationType {
  CREATE_LOAN(TestVestaOperationPriority.MEDIUM) {
    @Override
    public <T> T visit(VestaOperationVisitor<T> visitor) {
      return visitor.visitCreateLoan();
    }
  },
  APPLICATION_UPDATE(TestVestaOperationPriority.LOW) {
    @Override
    public <T> T visit(VestaOperationVisitor<T> visitor) {
      return visitor.visitApplicationUpdate();
    }
  },
  CREDIT_PULL(TestVestaOperationPriority.HIGH) {
    @Override
    public <T> T visit(VestaOperationVisitor<T> visitor) {
      return visitor.visitCreditPull();
    }
  },
  DOCUMENT_UPLOAD(TestVestaOperationPriority.HIGH) {
    @Override
    public <T> T visit(VestaOperationVisitor<T> visitor) {
      return visitor.visitDocumentUpload();
    }
  },
  DOCUMENT_DOWNLOAD(TestVestaOperationPriority.HIGH) {
    @Override
    public <T> T visit(VestaOperationVisitor<T> visitor) {
      return visitor.visitDocumentDownload();
    }
  },
  DOCUMENT_UPDATE_STATUS(TestVestaOperationPriority.HIGH) {
    @Override
    public <T> T visit(VestaOperationVisitor<T> visitor) {
      return visitor.visitDocumentUpdateStatus();
    }
  },
  VESTA_WEBHOOK_APPLICATION_UPDATE(TestVestaOperationPriority.LOW) {
    @Override
    public <T> T visit(VestaOperationVisitor<T> visitor) {
      return visitor.visitVestaWebhookApplicationUpdate();
    }
  },
  VESTA_WEBHOOK_DOC_UPLOAD(TestVestaOperationPriority.LOW) {
    @Override
    public <T> T visit(VestaOperationVisitor<T> visitor) {
      return visitor.visitVestaWebhookDocumentUpload();
    }
  },
  VESTA_WEBHOOK_DOC_METADATA_UPDATE(TestVestaOperationPriority.LOW) {
    @Override
    public <T> T visit(VestaOperationVisitor<T> visitor) {
      return visitor.visitVestaWebhookDocumentMetadataUpdate();
    }
  },
  NO_OP(TestVestaOperationPriority.LOW) {
    @Override
    public <T> T visit(VestaOperationVisitor<T> visitor) {
      return visitor.visitNoOp();
    }
  };

  private final TestVestaOperationPriority priority;

  TestVestaOperationType(TestVestaOperationPriority priority) {
    this.priority = priority;
  }

  public TestVestaOperationPriority priority() {
    return priority;
  }

  public abstract <T> T visit(VestaOperationVisitor<T> visitor);

  public interface VestaOperationVisitor<T> {

    T visitCreateLoan();

    T visitApplicationUpdate();

    T visitCreditPull();

    T visitDocumentUpload();

    T visitDocumentDownload();

    T visitDocumentUpdateStatus();

    T visitNoOp();

    T visitVestaWebhookApplicationUpdate();

    T visitVestaWebhookDocumentUpload();

    T visitVestaWebhookDocumentMetadataUpdate();

  }

  public interface ThrowingVestaOperationVisitor<T> extends VestaOperationVisitor<T> {

    @Override
    default T visitCreateLoan() {
      throw new UnsupportedOperationException();
    }

    @Override
    default T visitApplicationUpdate() {
      throw new UnsupportedOperationException();
    }

    @Override
    default T visitCreditPull() {
      throw new UnsupportedOperationException();
    }

    @Override
    default T visitDocumentUpload() {
      throw new UnsupportedOperationException();
    }

    @Override
    default T visitDocumentDownload() {
      throw new UnsupportedOperationException();
    }

    @Override
    default T visitDocumentUpdateStatus() {
      throw new UnsupportedOperationException();
    }

    @Override
    default T visitNoOp() {
      throw new UnsupportedOperationException();
    }

    @Override
    default T visitVestaWebhookApplicationUpdate() {
      throw new UnsupportedOperationException();
    }

    @Override
    default T visitVestaWebhookDocumentUpload() {
      throw new UnsupportedOperationException();
    }

    @Override
    default T visitVestaWebhookDocumentMetadataUpdate() {
      throw new UnsupportedOperationException();
    }

  }
}
