package com.wealthfront.voyager;

import static com.kaching.platform.queryengine.Owner.owner;
import static com.kaching.platform.queryengine.SoleOwnership.soleOwnership;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.kaching.platform.queryengine.OwnerExtractor;
import com.kaching.platform.queryengine.OwnerExtractorModule;
import com.kaching.user.UserId;
import com.kaching.util.id.External;

public class VoyagerTestOwnerExtractorModule extends AbstractModule {

  @Override
  protected void configure() {
    install(new OwnerExtractorModule());
  }

  @Provides
  public OwnerExtractor<External<UserId>> externalUserIdOwnerExtractor() {
    return (externalUserId, dbSession) -> soleOwnership(owner(new UserId(Long.parseLong(externalUserId.getId()))));
  }

}