package com.wealthfront.util.collections;

import static com.wealthfront.test.Assert.assertArrayEquals;
import static com.wealthfront.test.Assert.assertFalse;
import static com.wealthfront.test.Assert.assertThrows;
import static com.wealthfront.test.Assert.assertTrue;
import static org.junit.Assert.assertEquals;

import java.util.BitSet;

import org.junit.Test;

import com.google.common.collect.ImmutableList;

public class AtomicBitSetTest {
  
  @Test
  public void test_getAndSet_small() {
    AtomicBitSet atomicBitSet = new AtomicBitSet(3);
    assertEquals(ImmutableList.of(false, false, false), atomicBitSet.asBooleanList());
    assertFalse(atomicBitSet.getAndSetVolatile(0, true));
    
    assertEquals(ImmutableList.of(true, false, false), atomicBitSet.asBooleanList());
    assertTrue(atomicBitSet.getAndSetVolatile(0, true));
    assertEquals(ImmutableList.of(true, false, false), atomicBitSet.asBooleanList());
    
    atomicBitSet.getAndSetVolatile(0, true);
    assertArrayEquals(new int[] {0b0000000000000000000000000000001}, atomicBitSet.getWords());
    assertEquals(ImmutableList.of(true, false, false), atomicBitSet.asBooleanList());
    atomicBitSet.getAndSetVolatile(2, true);
    assertEquals(ImmutableList.of(true, false, true), atomicBitSet.asBooleanList());
    assertFalse(atomicBitSet.getAndSetVolatile(1, true));
    assertEquals(ImmutableList.of(true, true, true), atomicBitSet.asBooleanList());
    atomicBitSet.getAndSetVolatile(1, true);
    assertEquals(ImmutableList.of(true, true, true), atomicBitSet.asBooleanList());
    
    assertArrayEquals(new int[] {0b0000000000000000000000000000111}, atomicBitSet.getWords());
    
    assertTrue(atomicBitSet.getAndSetVolatile(1, false));
    assertEquals(ImmutableList.of(true, false, true), atomicBitSet.asBooleanList());
    atomicBitSet.getAndSetVolatile(0, false);
    assertEquals(ImmutableList.of(false, false, true), atomicBitSet.asBooleanList());
    assertArrayEquals(new int[] {0b0000000000000000000000000000100}, atomicBitSet.getWords());
  }

  @Test
  public void test_getAndSet_large() {
    AtomicBitSet atomicBitSet = new AtomicBitSet(62);
    assertArrayEquals(new int[] {
        0b00000000000000000000000000000000,
        0b00000000000000000000000000000000,
    }, atomicBitSet.getWords());
    
    atomicBitSet.getAndSetVolatile(0, true);
    assertArrayEquals(new int[] {
        0b00000000000000000000000000000001,
        0b00000000000000000000000000000000,
    }, atomicBitSet.getWords());

    atomicBitSet.getAndSetVolatile(31, true);
    assertArrayEquals(new int[] {
        0b10000000000000000000000000000001,
        0b00000000000000000000000000000000,
    }, atomicBitSet.getWords());

    assertFalse(atomicBitSet.getVolatile(32));
    atomicBitSet.getAndSetVolatile(32, true);
    assertTrue(atomicBitSet.getVolatile(32));
    assertArrayEquals(new int[] {
        0b10000000000000000000000000000001,
        0b00000000000000000000000000000001,
    }, atomicBitSet.getWords());

    assertFalse(atomicBitSet.getVolatile(61));
    assertFalse(atomicBitSet.getAndSetVolatile(61, true));
    assertArrayEquals(new int[] {
        0b10000000000000000000000000000001,
        0b00100000000000000000000000000001,
    }, atomicBitSet.getWords());
    assertTrue(atomicBitSet.getVolatile(61));

    assertThrows(IndexOutOfBoundsException.class, () -> atomicBitSet.getAndSetVolatile(62, true));
    assertArrayEquals(new int[] {
        0b10000000000000000000000000000001,
        0b00100000000000000000000000000001,
    }, atomicBitSet.getWords());
    
    assertTrue(atomicBitSet.getAndSetVolatile(61, false));
    assertArrayEquals(new int[] {
        0b10000000000000000000000000000001,
        0b00000000000000000000000000000001,
    }, atomicBitSet.getWords());
    assertFalse(atomicBitSet.getAndSetVolatile(61, false));
    assertFalse(atomicBitSet.getVolatile(61));
  }
  
  @Test
  public void getAndSet_indexOutOfBounds() {
    AtomicBitSet atomicBitSet = new AtomicBitSet(3);
    assertThrows(IndexOutOfBoundsException.class, () -> atomicBitSet.getAndSetVolatile(-1, true));
    assertThrows(IndexOutOfBoundsException.class, () -> atomicBitSet.getAndSetVolatile(3, true));
    assertThrows(IndexOutOfBoundsException.class, () -> atomicBitSet.getVolatile(-1));
    assertThrows(IndexOutOfBoundsException.class, () -> atomicBitSet.getVolatile(3));
  }
  
  @Test
  public void toBitSetVolatile() {
    AtomicBitSet atomicBitSet = new AtomicBitSet(34);
    atomicBitSet.getAndSetVolatile(0, true);
    atomicBitSet.getAndSetVolatile(2, true);
    atomicBitSet.getAndSetVolatile(31, true);
    atomicBitSet.getAndSetVolatile(27, true);
    atomicBitSet.getAndSetVolatile(33, true);
    
    BitSet bitSet = atomicBitSet.toBitSetVolatile();
    for (int i = 0; i < 34; i++) {
      assertEquals(atomicBitSet.getVolatile(i), bitSet.get(i));
    }
  }

  @Test
  public void toBitSetVolatile_exactSize() {
    AtomicBitSet atomicBitSet = new AtomicBitSet(64);
    atomicBitSet.getAndSetVolatile(0, true);
    atomicBitSet.getAndSetVolatile(2, true);
    atomicBitSet.getAndSetVolatile(31, true);
    atomicBitSet.getAndSetVolatile(27, true);
    atomicBitSet.getAndSetVolatile(33, true);

    BitSet bitSet = atomicBitSet.toBitSetVolatile();
    for (int i = 0; i < 64; i++) {
      assertEquals(atomicBitSet.getVolatile(i), bitSet.get(i));
    }
  }

}