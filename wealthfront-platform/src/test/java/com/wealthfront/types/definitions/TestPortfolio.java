package com.wealthfront.types.definitions;

import java.util.List;
import java.util.Map;

import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;

@ExposeType(
    namespace = ExposeType.RewriteNamespace.DO_NOT_COPY,
    value = {
        ExposeTo.LOCAL,
        ExposeTo.BACKEND,
        ExposeTo.API_SERVER,
        ExposeTo.FRONTEND
    }
)
@Entity(discriminatorName = "type", subclasses = {
    TestStockPortfolio.class
})
public abstract class TestPortfolio extends PortfolioGrandfather{

  @Value(
      optional = true,
      nullable = true
  )
  private Map<String, Map<String, List<TestStockPortfolioPosition>>> parentalMapListPositions;
}
